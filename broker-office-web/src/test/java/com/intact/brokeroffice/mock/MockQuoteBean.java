package com.intact.brokeroffice.mock;

import org.joda.time.DateTime;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.intact.brokeroffice.business.quote.QuotesBean;

public class MockQuoteBean {

	public static QuotesBean createQuoteBean() {
		
		QuotesBean quoteBean = new QuotesBean();
		quoteBean.setBrokerNumber("12345");
		quoteBean.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
		quoteBean.setQuoteSource("QH");		
		quoteBean.setAgreementNumber("parent");
		
		quoteBean.setLastUpdate(new DateTime(2017, 5, 5, 8, 0).toDate());
		
		return quoteBean;
	}
}
