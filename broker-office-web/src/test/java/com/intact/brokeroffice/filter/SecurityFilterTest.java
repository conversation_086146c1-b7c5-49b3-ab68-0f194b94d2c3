package com.intact.brokeroffice.filter;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.web.api.auth.TamTokensEnum;

class SecurityFilterTest {

	private SecurityFilter securityFilter = new SecurityFilter();
	
	private MockHttpServletRequest request;
	private MockHttpServletResponse response;
	private ServletRequest servletRequest;
	private ServletResponse servletResponse;
	private MockHttpSession session;

	@BeforeEach
	void setUp() throws Exception {
		session = new MockHttpSession();
		request = new MockHttpServletRequest();
		response = new MockHttpServletResponse();
		request.setSession(session);
		servletRequest = request;
		servletResponse = response;
		
		ReflectionTestUtils.setField(securityFilter, "ldapGroupProgramAdmins", "ldapGroupProgramAdmins");
		ReflectionTestUtils.setField(securityFilter, "ldapGroupAdmins", "ldapGroupAdmins");
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void doFilter() throws IOException, ServletException {
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "testRole");
		request.setRequestURI("/brokers/subbroker/list.jsf");
		
		securityFilter.doFilter(servletRequest, servletResponse, null);
		
		//assert?
	}

}
