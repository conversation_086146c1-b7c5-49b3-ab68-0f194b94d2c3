package com.intact.brokeroffice.actuator;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * Simple test controller to verify Spring MVC setup is working
 */
@RestController
@RequestMapping("/actuator")
public class TestController {

    /**
     * Simple test endpoint
     */
    @GetMapping(value = "/test", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<Map<String, Object>> test() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "OK");
        response.put("message", "Actuator endpoints are working!");
        response.put("timestamp", Instant.now().toString());
        return ResponseEntity.ok(response);
    }
}
