package com.intact.brokeroffice.controller.language;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import jakarta.faces.context.FacesContext;
import jakarta.servlet.http.HttpSession;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.intact.brokeroffice.controller.AntiCSRFController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.util.ProvinceCompanyConverter;

/**
 * The Class LanguageController.
 */
@Component
@Scope("session")
public class LanguageController extends AntiCSRFController{
	
	private static final String LANGUAGE_FILTER = "filter.language";
	private Map<String, Map<String, String>> filters = null;
	
	public LanguageController() {
		super();
		this.setFilters(new HashMap<String, Map<String, String>>());
		this.getFilters().put(LanguageController.LANGUAGE_FILTER, new HashMap<String, String>());
		this.getFilters().get(LanguageController.LANGUAGE_FILTER).put("fr", "fr");
		this.getFilters().get(LanguageController.LANGUAGE_FILTER).put("en", "en");
	}
	
	public Map<String, Map<String, String>> getFilters() {
		return this.filters;
	}

	public void setFilters(Map<String, Map<String, String>> filters) {
		this.filters = filters;
	}

	/**
	 * Gets the locale.
	 * 
	 * @return the locale
	 */
	public Locale getLocale() {
		
		Locale locale = (Locale) FacesContext.getCurrentInstance().getExternalContext().getSessionMap().get("locale");
		
		if (locale == null) {
			locale = getLocaleUrl();
			
			if (locale == null) {
				locale =  FacesContext.getCurrentInstance().getViewRoot().getLocale();
			}
			
		}
		
		return locale; 
	}

	/**
	 * Sets french locale.
	 * 
	 * BR5229 Changes the language of the Quote View. Maintains the language of
	 * the user (based on Broker Portal language at entry) on all other Web Zone
	 * pages
	 */
	public void french() {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		String province = ProvinceCompanyConverter.convertSubBrokerCompanyToProvince((String) session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));

		Locale aLocale = new Locale("fr", "CA");

		if (province != null) {
			aLocale = new Locale("fr", "CA", province);
		}

		FacesContext.getCurrentInstance().getViewRoot().setLocale(aLocale);
		FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("locale", aLocale);
		session.setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), aLocale.getLanguage());
	}

	/**
	 * Sets english locale.
	 * 
	 * BR5229 Changes the language of the Quote View. Maintains the language of
	 * the user (based on Broker Portal language at entry) on all other Web Zone
	 * pages
	 */
	public void english() {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		String province = ProvinceCompanyConverter.convertSubBrokerCompanyToProvince((String) session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));

		Locale aLocale = new Locale("en", "CA");
		if (province != null) {
			aLocale = new Locale("en", "CA", province);
		}
		
		FacesContext.getCurrentInstance().getViewRoot().setLocale(aLocale);
		FacesContext.getCurrentInstance().getExternalContext().getSessionMap().put("locale", aLocale);
		session.setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), aLocale.getLanguage());
	}

	/**
	 * Checks if language is english
	 * 
	 * @return true if english
	 */
	public Boolean getLanguageEnglish() {
		if (Locale.CANADA.getLanguage().equals(this.getLocale().getLanguage())) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;

	}

	/**
	 * Gets the Locale based on client selection language BR5229 Changes the
	 * language of the Quote View. Maintains the language of the user (based on
	 * Broker Portal language at entry) on all other Web Zone pages
	 * 
	 * @return
	 */
	public Locale getLocaleUrl() {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		String language = (String) session.getAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant());
		String province = ProvinceCompanyConverter.convertSubBrokerCompanyToProvince((String) session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));

		language = language != null ? language : "en";

		Locale aLocale = new Locale(language, "CA");
		if (province != null) {
			aLocale = new Locale(language, "CA", province);
		}

		if (context.getViewRoot() != null) {
			context.getViewRoot().setLocale(aLocale);
		}
		context.getExternalContext().getSessionMap().put("locale", aLocale);
		return aLocale;
	}

	/**
	 * Gets the locale for the quote view page
	 * 
	 * @return
	 */
	public Locale getLocaleQuote() {
		if (FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap().get("referenceNo") != null) {
			return getLocaleUrl();
		}
		return getLocale();
	}
	
	public String filterValue(String value) {
		return this.getFilters().get(LanguageController.LANGUAGE_FILTER).get(value);
	}
}
