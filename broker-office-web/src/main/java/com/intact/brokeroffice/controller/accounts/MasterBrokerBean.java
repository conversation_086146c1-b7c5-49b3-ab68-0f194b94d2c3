/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.accounts;

/**
 * The Class MasterBrokerBean.
 */
public class MasterBrokerBean {

	private String number;

	private String name;

	private Boolean selected;

	/**
	 * DEFAULT constructor
	 * 
	 */
	public MasterBrokerBean() {
		
	}

	/**
	 * Instantiates a new master broker bean.
	 * 
	 * @param aNumber the a number
	 * @param aName the a name
	 */
	public MasterBrokerBean(String aNumber, String aName) {
		this.number = aNumber;
		this.name = aName;
	}

	/**
	 * @return the name
	 */
	public String getName() {
		return this.name;
	}

	/**
	 * @param aName the name to set
	 */
	public void setName(String aName) {
		this.name = aName;
	}

	/**
	 * @return the number
	 */
	public String getNumber() {
		return this.number;
	}

	/**
	 * @param aNumber the number to set
	 */
	public void setNumber(String aNumber) {
		this.number = aNumber;
	}

	/**
	 * @return the selected
	 */
	public Boolean getSelected() {
		return this.selected;
	}

	/**
	 * @param aSelected the selected to set
	 */
	public void setSelected(Boolean aSelected) {
		this.selected = aSelected;
	}
}
