package com.intact.brokeroffice.controller.viewquote.offer;

import java.util.Locale;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

public interface IOfferAdapter<E extends VehicleOfferBean> {

	/**
	 * Populate the bean with the db model objects.
	 * 
	 * @param policyVersion
	 * @param anInsuranceRisk
	 * @param aBean
	 * @param locale
	 * @param aProvinceCode
	 * @param appMode
	 */
	void loadBean(InsuranceRisk anInsuranceRisk, E aBean, Locale locale, ProvinceCodeEnum aProvinceCode, ApplicationModeEnum appMode);

	/**
	 * Gets the consent for ontario model.
	 * 
	 * @param policyVersion the policy version
	 * 
	 * @return the marketing consent
	 */
	Boolean getConsent(PolicyVersion policyVersion);
}
