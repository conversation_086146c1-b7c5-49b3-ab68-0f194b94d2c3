package com.intact.brokeroffice.web.util;

import java.lang.reflect.Field;

import org.apache.log4j.Hierarchy;
import org.apache.log4j.LogManager;
import org.apache.log4j.spi.LoggerFactory;
import org.apache.log4j.spi.LoggerRepository;

public class SecureLog4jLogFactory implements LoggerFactory {

	private static boolean done = false;

	/**
	 * This constructor must be public so it can be accessed from within log4j
	 */
	public SecureLog4jLogFactory() {
	}

	/**
	 * Overridden to return instances of org.owasp.esapi.reference.Log4JLogger.
	 * 
	 * @param name The class name to return a logger for.
	 * @return org.owasp.esapi.reference.Log4JLogger
	 */
	public SecureLog4jLogger makeNewLoggerInstance(String name) {

		if (!this.done) {
			this.changeDefaultFactory();
			this.done=true;
		}
		
		return new SecureLog4jLogger(name);

	}

	protected void changeDefaultFactory() {
		LoggerRepository repository = LogManager.getRootLogger().getRoot().getHierarchy();

		try {
			Field field = Hierarchy.class.getDeclaredField("defaultFactory");
			field.setAccessible(true);
			field.set(repository, this);
			field.setAccessible(false);

		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
}

