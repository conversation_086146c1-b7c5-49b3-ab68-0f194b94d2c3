package com.intact.brokeroffice.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;
import org.springframework.core.annotation.Order;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.jta.JtaTransactionManager;

import java.time.LocalDate;

@Order(0)
@Configuration
@PropertySources(
    {
        @PropertySource("classpath:ldap.properties"),
        @PropertySource("classpath:plt-service.properties"),
        @PropertySource("classpath:cif-services.properties"),
        @PropertySource("classpath:brm-services.properties"),
        @PropertySource("classpath:brokerOffice-web.properties"),
        @PropertySource("classpath:plp-services.properties")
    }
)
@EnableTransactionManagement
public class AppConfig {

    @Value("${interest_date_on}")
    private String interestDateOn;

    @Bean
    public LocalDate ontarioInterestChangeDate() {
        return LocalDate.parse(interestDateOn);
    }


    @Bean
    public PlatformTransactionManager transactionManager() {
        JtaTransactionManager transactionManager = new JtaTransactionManager();
        transactionManager.setDefaultTimeout(300); // Set timeout to 300 seconds (5 minutes)
        return transactionManager;
    }
}
