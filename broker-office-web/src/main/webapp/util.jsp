<%@page import="com.intact.common.security.SecurityUtility"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
//Security Check - This page should only valid in non prod environment or in prod from an Intact internal IP
if (SecurityUtility.getInstance().getIpToolbox().isIpFromIntact(request) || SecurityUtility.getInstance().getIpToolbox().isIpFromInternal(request)){
%>
<HTML>
<HEAD>
<%@ page language="java" contentType="text/html; charset=ISO-8859-1" pageEncoding="ISO-8859-1"
%>
<META http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<TITLE>util.jsp</TITLE>
</HEAD>
<BODY>
 
<%
//PRINT THE IP ADDRESS AND HOSTNAME OF THE SERVER
    try {

	java.net.InetAddress addr = java.net.InetAddress.getLocalHost();

        // Get IP Address
        byte[] ipAddr = addr.getAddress();

      	String ipAddrStr = "";
        for (int i=0; i<ipAddr.length; i++) {
            if (i > 0) {
                ipAddrStr += ".";
            }
            ipAddrStr += ipAddr[i]&0xFF;
        }
	out.println("IP: "+ipAddrStr + "<br>");

    
        // Get hostname
	out.println("HOSTNAME: "+addr.getHostName());

    } catch (java.net.UnknownHostException e) {
	System.out.println("error will getting hostname or ip address");
    }

%>
server=<c:out value="${request.serverName}" />
<p><STRONG>SESSION</strong>
 	<table width=100% border=1>
    <c:forEach var="sess" items="${sessionScope}">
       <tr><td><c:out value="${sess.key}" /></td><td><c:out value="${sess.value}" /></td></tr>
    </c:forEach>
      </table><br></p>
<p><strong>HEADER</strong>
<table width=100% border=5>
<c:forEach var="hv" items="${header}">
	<tr><td><c:out value="${hv.key}" /></td><td><c:out value="${hv.value}" /></td></tr>
</c:forEach>
 </table><br></p>
</BODY>
</HTML>
<%
}else{
%>
<HTML>
<BODY>
Not Autorized
</BODY>
</HTML>
<%
}
%>
