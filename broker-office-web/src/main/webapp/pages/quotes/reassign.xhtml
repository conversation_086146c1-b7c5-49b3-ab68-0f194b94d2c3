<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="msg_reassign" basename="com.intact.brokeroffice.controller.reassign.reassign"/>

	<p:panel styleClass="reassignPanel reassignPanel-border">

		<h:outputText value="#{msg_reassign['title']}" styleClass="title"/>

		<p:panelGrid columns="1" cellspacing="0" cellpadding="0">
			<h:outputFormat value="#{msg_reassign['form.text']}" rendered="#{searchController.nbQuotesSelected gt 1}">
				<f:param value="#{searchController.nbQuotesSelected}"/>
			</h:outputFormat>
			<h:outputFormat value="#{msg_reassign['form.text.one']}" rendered="#{searchController.nbQuotesSelected == 1}">
				<f:param value="#{searchController.nbQuotesSelected}"/>
			</h:outputFormat>

			<p:panelGrid columns="3" layout="tabular" columnClasses="reassignColumn1, reassignColumn2, reassignColumn3" cellspacing="0" cellpadding="0" rowClasses="reassignRow">
				<h:outputText value="#{msg_reassign['form.broker']}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0" >
					<h:selectOneMenu id="broker" value="#{reassignController.broker}" styleClass="input_reassignToBroker" >
						<f:selectItems value="#{reassignController.brokers}" />
					</h:selectOneMenu>
					<p:message styleClass="errorMessage" for="broker" />
				</p:panelGrid>
			</p:panelGrid>
		</p:panelGrid>
		
		<p:panel styleClass="btn-top-margin-reassignQ reassign-btn-group">
			<p:commandLink id="submitLink" styleClass="actionBtn btnArrow"
						   action="#{reassignController.reassign}"
			               update=":mainTab:quoteListForm,:mainTab:reassignForm"
						   oncomplete="Primefaces.hideModalPanel('mainTab\:modalPanelReassign');">
				<h:outputText value="#{global['button.submit']}"/>
			</p:commandLink>

			<p:commandLink id="cancelLink" styleClass="actionBtn"
						   onclick="Primefaces.hideModalPanel('mainTab\:modalPanelReassign');"
						   update="mainTab:quoteListForm">
				<h:outputText value="#{global['button.cancel']}"/>
			</p:commandLink>
		</p:panel>

	</p:panel>
</ui:composition>
