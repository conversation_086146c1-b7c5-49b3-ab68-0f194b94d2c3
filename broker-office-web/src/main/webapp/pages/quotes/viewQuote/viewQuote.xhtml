<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:ui="http://java.sun.com/jsf/facelets"
      xmlns:h="http://java.sun.com/jsf/html"
      xmlns:f="http://java.sun.com/jsf/core"
      xmlns:p="http://primefaces.org/ui">

<f:view locale="#{languageController.localeQuote}"/>
<h:head>
    <meta http-equiv="X-UA-Compatible" content="IE=8"/>

    <link href="../../../style/richComponent.css" rel="stylesheet" type="text/css" media="all"/>
    <link href="../../../style/main.css" rel="stylesheet" type="text/css" media="all"/>
    <script src="../../../javascript/richComponent.js" type="text/javascript"/>
    <script src="../../../javascript/search.js" type="text/javascript"/>
</h:head>

<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
<f:loadBundle var="msg" basename="com.intact.brokeroffice.controller.viewquote.viewquote"/>
<f:loadBundle var="msg_client" basename="com.intact.brokeroffice.controller.viewquote.client"/>
<f:loadBundle var="goBrio" basename="gobrio"/>

<h:head>
    <title>#{msg['window.title']}</title>
</h:head>

<ui:param name="viewQuoteController" value="#{factoryViewQuoteController.viewQuoteController}"/>

<p:panel id="viewQuotePanel" styleClass="main"
         rendered="#{viewQuoteController.isAllowed(param.referenceNo)}">

    <ui:include src="/pages/common/banner.xhtml"/>
    <ui:include src="/templates/basePage.xhtml"/>

    <p:panel styleClass="header">
        <p:spacer height="100px"/>
        <h:graphicImage url="#{global['global.image.relative']}/intactLogoLeftLines.png" alt=""/>
    </p:panel>

    <h:form id="languageForm">
        <p:panel id="language" styleClass="language headerSectionR">
            <h:inputHidden id="AntiCSRFToken" value="#{languageController.tokenCSRF}"/>

            <p:commandLink value="#{global['language.switch']}"
                           action="#{viewQuoteController.changeLanguage('en')}"
                           rendered="#{!languageController.languageEnglish}"
                           update="viewQuotePanel" styleClass="link_languageEN">
                <f:param name="referenceNo" value="#{param.referenceNo}"/>
                <f:param name="lineOfBusiness" value="#{param.lineOfBusiness}"/>
                <f:param name="lineOfInsurance" value="#{param.lineOfInsurance}"/>
                <f:param name="searchIndex" value="#{param.searchIndex}"/>
            </p:commandLink>
            <p:commandLink value="#{global['language.switch']}"
                           action="#{viewQuoteController.changeLanguage('fr')}"
                           rendered="#{languageController.languageEnglish}"
                           update="viewQuotePanel" styleClass="link_languageFR">
                <f:param name="referenceNo" value="#{param.referenceNo}"/>
                <f:param name="lineOfBusiness" value="#{param.lineOfBusiness}"/>
                <f:param name="lineOfInsurance" value="#{param.lineOfInsurance}"/>
                <f:param name="searchIndex" value="#{param.searchIndex}"/>
            </p:commandLink>
        </p:panel>
    </h:form>

    <h:form id="viewQuoteForm" styleClass="viewQuote-wrap">
        <h:inputHidden id="AntiCSRFToken"
                       value="#{languageController.tokenCSRF}"/>
        <!--  PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
        <p:panel style="display:none">
            <h:commandLink styleClass="actionBtnRightHover">
                <h:outputText value="invisible right"/>
            </h:commandLink>

            <h:commandLink styleClass="actionBtnLeftHover">
                <h:outputText value="invisible left"/>
            </h:commandLink>
        </p:panel>

        <h:inputHidden id="referenceNo" value="#{viewQuoteController.quote.id}"/>
        <!-- 			<h:inputHidden id="driverName"  value="#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren('general_policyHolderName').pieces[1].value} " /> -->
        <h:inputHidden id="driverName" value=""/>

        <p:panel id="viewQuoteInfo" styleClass="formViewQuote">

            <h:panelGroup colspan="1"
                          rendered="#{permissionController.checkUpload and viewQuoteController.showRemoteLink}">
                <p:commandLink styleClass="link_goToGoBrio successMessage underline-text"
                               onclick="window.open('#{viewQuoteController.remoteSystemUrl}');"/>
                <h:outputFormat value="#{msg_client['client.upload.remote.system.url']}"
                                escape="false">
                    <f:param value="#{goBrio[viewQuoteController.remoteSystemUrl]}"/>
                    <f:param
                            value="&#60;span id='upload_refNo'&#62;#{viewQuoteController.referenceLegacyNo}&#60;/span&#62;"
                            escape="false"/>
                    <f:param name="applicationMode"
                             value="#{viewQuoteController.quote.applicationMode}"/>
                </h:outputFormat>
                <p:spacer/>
            </h:panelGroup>

            <br/>

            <p:panelGrid id="uploadToGOBRIO" columns="1" cellspacing="0" cellpadding="0"
                         width="100%"
                         rendered="#{permissionController.checkUpload and viewQuoteController.checkUpload and provinceController.showUpload(viewQuoteController.quote.agreementNumber)}">
                <p:commandLink id="uploadLink1" value="#{msg['link.upload']}"
                               action="#{viewQuoteController.upload(param.referenceNo)}"
                               update="viewQuotePanel" styleClass="link_upload1">
                    <f:param name="referenceNo" value="#{param.referenceNo}"/>
                    <f:param name="searchIndex" value="#{param.searchIndex}"/>
                    <f:param name="applicationMode"
                             value="#{viewQuoteController.quote.applicationMode}"/>
                </p:commandLink>
            </p:panelGrid>
            <p:panelGrid id="assignQuote" columns="1" cellspacing="0" cellpadding="0" width="100%"
                         rendered="#{permissionController.checkAssign(viewQuoteController.quote.brokerNumber) and viewQuoteController.checkAssign and provinceController.showAssign and viewQuoteController.assignSucceed == null}">
                <p:commandLink id="assign1" value="#{msg['link.assign']}"
                               action="#{viewQuoteController.assign(param.referenceNo)}"
                               update="viewQuotePanel" styleClass="link_upload1">
                    <f:param name="referenceNo" value="#{param.referenceNo}"/>
                    <f:param name="searchIndex" value="#{param.searchIndex}"/>
                    <f:param name="applicationMode"
                             value="#{viewQuoteController.quote.applicationMode}"/>
                </p:commandLink>
            </p:panelGrid>
            <br/>

            <!-- Download broker PDF -->
            <p:panelGrid id="download"
                         columns="1"
                         cellspacing="0"
                         cellpadding="0"
                         width="100%"
                         rendered="#{viewQuoteController.showDownloadLink}">
                <p:commandLink id="download1"
                               value="#{msg['link.download']}"
                               action="#{viewQuoteController.upload(param.referenceNo)}"
                               update="viewQuotePanel"
                               styleClass="link_upload1"
                               ajax="false">
                    <f:param name="referenceNo" value="#{param.referenceNo}"/>
                    <f:param name="searchIndex" value="#{param.searchIndex}"/>
                    <f:param name="applicationMode" value="#{viewQuoteController.quote.applicationMode}"/>
                </p:commandLink>
            </p:panelGrid>
            <br/>
            <!-- Create Dialer File -->
            <p:panelGrid id="dialer"
                         class="headerSection"
                         columns="1"
                         cellspacing="0"
                         cellpadding="0"
                         width="100%"
                         rendered="#{viewQuoteController.showDialerLink}">
                <p:commandLink id="dialer1"
                               value="#{msg['link.dialer']}"
                               action="#{viewQuoteController.createDialerFile}"
                               update="viewQuotePanel"
                               styleClass="link_upload1">
                    <f:param name="referenceNo" value="#{param.referenceNo}"/>
                    <f:param name="searchIndex" value="#{param.searchIndex}"/>
                    <f:param name="applicationMode" value="#{viewQuoteController.quote.applicationMode}"/>
                </p:commandLink>
            </p:panelGrid>

            <h:panelGroup colspan="1">
                <br/>
                <span id="upload_messages">
						<h:outputText styleClass="successMessage"
                                      value="#{msg_client['message.upload.to.goBrio.confirmation']}"
                                      rendered="#{viewQuoteController.uploadSucceed}"/>
						<h:outputText styleClass="errorMessage"
                                      value="#{msg_client['message.upload.to.goBrio.error']}"
                                      rendered="#{viewQuoteController.uploadSucceed != null and !viewQuoteController.uploadSucceed}"/>
						<h:outputText styleClass="successMessage" escape="false"
                                      value="#{viewQuoteController.dialerOutput}"
                                      rendered="#{viewQuoteController.dialerSucceed}"/>
						<h:outputText styleClass="errorMessage" escape="false"
                                      value="#{viewQuoteController.dialerOutput}"
                                      rendered="#{viewQuoteController.dialerSucceed != null and viewQuoteController.dialerOutput != null and !viewQuoteController.dialerSucceed}"/>
                        <h:outputText styleClass="successMessage"
                                      value="#{msg_client['message.assign.confirmation']}"
                                      rendered="#{viewQuoteController.assignSucceed}"/>
						<h:outputText styleClass="errorMessage"
                                      value="#{msg_client['message.assign.error']}"
                                      rendered="#{viewQuoteController.assignSucceed != null and !viewQuoteController.assignSucceed}"/>
					</span>
            </h:panelGroup>


            <p:panelGrid columns="1" id="collapseBtn" class="headerSectionR" cellspacing="0" cellpadding="0">
                <p:panel id="collapseAllaqua">
                    <h:graphicImage onclick="PF('quoteDetailsAccordion').unselectAll();" styleClass="collapseBtn"
                                    url="/image/iconCollapseAllaqua.gif"/>
                </p:panel>
                <p:panel id="expandAllaqua">
                    <h:graphicImage onclick="PF('quoteDetailsAccordion').selectAll();" styleClass="collapseBtn"
                                    url="/image/iconExpandAllaqua.gif"/>
                </p:panel>
            </p:panelGrid>

            <p:accordionPanel multiple="true" activeIndex="all" widgetVar="quoteDetailsAccordion">

                <!-- Client info -->
                <p:tab id="client_info"
                       rendered="#{not empty viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren('general')}"
                       title="#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren('general').pieces[0].value}">

                    <ui:include src="/pages/quotes/viewQuote/section-message.xhtml">
                        <ui:param name="section" value="important_messages"/>
                        <ui:param name="viewQuoteController" value="#{viewQuoteController}"/>
                    </ui:include>

                    <ui:include src="/pages/quotes/viewQuote/section-general.xhtml">
                        <ui:param name="section" value="general_info"/>
                        <ui:param name="viewQuoteController" value="#{viewQuoteController}"/>
                    </ui:include>

                    <ui:include src="/pages/quotes/viewQuote/section-activity.xhtml">
                        <ui:param name="viewQuoteController" value="#{viewQuoteController}"/>
                    </ui:include>

                    <ui:include src="/pages/quotes/viewQuote/section-vehicle.xhtml">
                        <ui:param name="viewQuoteController" value="#{viewQuoteController}"/>
                    </ui:include>
                </p:tab>
            </p:accordionPanel>

            <script type="text/javascript" language="JavaScript">
                // Used in page body. See src/main/webapp/templates/basePage.xhtml
                // noinspection JSUnusedLocalSymbols
                function initWindowTitle() {
                    document.title = '#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren('
                    general_policyHolderName
                    ').pieces[1].value}';
                }
            </script>

            <p:spacer height="20px"/>

            <p:panelGrid id="uploadToGOBRIO2" class="headerSection" columns="1" cellspacing="0" cellpadding="0"
                         width="100%"
                         rendered="#{permissionController.checkUpload and viewQuoteController.checkUpload and provinceController.showUpload(viewQuoteController.quote.agreementNumber)}">>
                <p:commandLink id="uploadLink2" value="#{msg['link.upload']}"
                               action="#{viewQuoteController.upload(param.referenceNo)}"
                               update="viewQuotePanel" styleClass="link_upload1">
                    <f:param name="referenceNo" value="#{param.referenceNo}"/>
                    <f:param name="searchIndex" value="#{param.searchIndex}"/>
                    <f:param name="applicationMode"
                             value="#{viewQuoteController.quote.applicationMode}"/>
                </p:commandLink>
            </p:panelGrid>

        </p:panel>

    </h:form>

    <p:spacer height="10px"/>
</p:panel>

<p:panel id="accesDeniedPanel" styleClass="main"
         rendered="#{!viewQuoteController.isAllowed(param.referenceNo)}">

    <p:panel styleClass="header">
        <p:spacer height="100px"/>
        <h:graphicImage url="#{global['global.image.relative']}/intactLogoLeftLines.png" alt=""/>
    </p:panel>

    <p:panel styleClass="contents accessDeniedPadding">
        <p:panel styleClass="border-mid-content">
            <h:graphicImage url="/image/contentTopGrey.png" styleClass="breadcrumb" alt=""/>
            <h:form id="viewQuoteFormDenied" styleClass="info-accessdenied-in-tab">
                <h:inputHidden id="AntiCSRFToken" value="#{provinceController.tokenCSRF}"/>
                <h:outputText width="926px" styleClass="redTitle" value="#{msg['accessDenied']}"/>
                <br/>
                <h:outputText styleClass="subtitle-accessdenied"
                              value="#{viewQuoteController.currentDate}"
                              rendered="#{!provinceController.company3}">
                    <f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm:ss"
                                       timeZone="US/Eastern"/>
                </h:outputText>
                <h:outputText styleClass="subtitle-accessdenied"
                              value="#{viewQuoteController.currentDate}"
                              rendered="#{provinceController.company3}">
                    <f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm:ss"
                                       timeZone="Canada/Mountain"/>
                </h:outputText>
                <p:panel styleClass="leftfullpart line845"></p:panel>
                <h:graphicImage url="/image/ligneVirgule.png"
                                styleClass="rightpart margin-right-accessdenied"/>

                <p:panel styleClass="accessdenied-text">
                    <h:outputText styleClass="subtitle-accessdenied"
                                  value="#{msg['message.quote.has.been.reassigned']}"
                                  rendered="#{!viewQuoteController.quoteAccess}"/>
                    <h:outputText styleClass="subtitle-accessdenied"
                                  value="#{msg['message.region.changed']}"
                                  rendered="#{!viewQuoteController.getSameCompany(param.referenceNo)}"/>

                    <br/><br/>
                    <h:commandLink onclick="closeAndRefresh()"
                                   styleClass="link_accessDeniedBackButton actionBtn floatCenter"
                                   reRender="brokerPanel">
    							<span class="left">
    								<h:graphicImage url="/image/btnLeftArrow.png" alt=""/>
									<h:outputText value="#{global['form.button.back']}"/>
		    					</span>
                    </h:commandLink>
                </p:panel>
            </h:form>
            <h:graphicImage url="/image/contentBot966.png" alt=""/>
        </p:panel>
        <p:spacer height="10px"/>
    </p:panel>
</p:panel>

</html>
