<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:p="http://primefaces.org/ui">

	<ui:repeat value="#{coverageSection}" var="tab">
		<div class="quote-detail-section">
			<h:outputText value="#{tab.pieces[0].value}" styleClass="blue-top-subsection"/>
			<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
				<p:dataTable value="#{tab.childrens}" var="childPiece" styleClass="subformQuote" >
	
					<p:column>
						<span id="#{childPiece.value}_question"><h:outputText value="#{childPiece.pieces[0].value}" /></span>
					</p:column>
	
					<p:column>
						<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
					</p:column>
	
					<p:column>
						<span id="#{childPiece.value}_response">
							<h:outputText value="#{childPiece.pieces[1].value}"/>
						</span>
					</p:column>
	
				</p:dataTable>
			</p:panelGrid>
		</div>
	</ui:repeat>



</ui:composition>
