<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<div class="quote-detail-section">
		<h:outputText value="&#160;" styleClass="blue-top-subsection"/>
		<h:panelGrid columns="2" columnClasses="paymentColumn, paymentColumn2" styleClass="formQuote">
			<p:panel styleClass="payment-detail">
				<h:panelGrid columns="1" >
					<p:dataTable var="vehicle_offer2" rowIndexVar="rowIndex"  value="#{section.childrens}">

						<p:column colspan="3" styleClass="payment-objectName">
							<h:outputText id="vehicle_offer_#{rowIndex}" styleClass="black-label-payment vehicle-#{rowIndex}" value="#{vehicle_offer2.pieces[0].value}"/>
						</p:column>

							<p:column breakBefore="true" styleClass="black-label-payment premiumColumn1">
								<h:outputText value="#{vehicle_offer2.pieces[1].value}" styleClass="premium-#{rowIndex}" />
							</p:column>

							<p:column styleClass="premiumColumn2">
									   <span id="annual_premium_with_taxes_#{rowIndex}">
										 <h:outputText styleClass="annualpremiumWithTaxes-#{rowIndex}" value="#{vehicle_offer2.pieces[2].value}"><f:convertNumber  type="currency" /></h:outputText>
									 </span>
							</p:column>

							<p:column styleClass="red-label-payment premiumColumn3">
									 <span id="monthly_premium_with_taxes_#{rowIndex}">
										<h:outputText styleClass="monthlypremiumWithTaxes-#{rowIndex}" value="#{vehicle_offer2.pieces[3].value}"><f:convertNumber type="currency" /></h:outputText>
									</span>
							</p:column>
						
					</p:dataTable>
				</h:panelGrid>
				<h:panelGrid columns="1" styleClass="padding-space">
					<h:outputText value="#{section.pieces[0].value}" />
				</h:panelGrid>
			</p:panel>
		</h:panelGrid>
	</div>



</ui:composition>
