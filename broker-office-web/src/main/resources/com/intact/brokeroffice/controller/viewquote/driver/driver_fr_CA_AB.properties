#**************** POLICY HOLDER SECTION *********************** 

driver.aboutyou.permissioninfos=J'autorise Intact Assurance et mon courtier assigné à recueillir, utiliser, et communiquer mes renseignements personnels comme le permet la loi, dans le but de créer un profil à mon nom et de me fournir une soumission d'assurance auto.

driver.aboutyou.licencesuspended						= Est-il arrivé, au cours des 6 dernières années, qu'un permis de conduire, un permis de véhicule, ou autre autorisation semblable, délivré en votre nom ou au nom de tout autre conducteur à inclure à la présente soumission, ait été ou continue d'être suspendu, annulé ou échu?
driver.aboutyou.insurancerefusal						= Est-il arrivé, au cours des 3 dernières années, qu'un assureur annule, résilie ou refuse de renouveler ou d'émettre  un contrat d'assurance automobile délivré à vous ou à tout conducteur énuméré sur la soumission ou qu'une demande de règlement ait été refusée pour cause de déclaration inexacte importante?
driver.aboutyou.policyrefusal.because.of.nonpayment		= La police d'assurance a-t-elle été annulée ou refusée pour cause de non paiement?
driver.aboutyou.policyrefusal.how.many.times 			= Combien de fois est-ce que vous ou tout conducteur énuméré sur la soumission avez vu votre assurance annulée ou refusée pour cause de non paiement?
driver.aboutyou.other.licensed.drivers					= Est-ce que d'autres conducteurs titulaires de permis (qui ne sont pas déjà énumérés) habitent au domicile de l'assuré ou est-ce que d'autres conducteurs réguliers habitent ailleurs que chez l'assuré?
driver.aboutyou.all.drivers.own.insurance           	= Est-ce que TOUS les conducteurs possèdent leur propre assurance automobile?
driver.aboutyou.savewhenvehicleandhomeareinsuredwithus	= 50% de nos clients détiennent à la fois leur assurance auto et habitation avec nous et économisent encore plus. Voulez-vous inclure le rabais multi-contrats mon auto, mon foyer<sup>MC</sup> à votre soumission?
driver.aboutyou.howlonglivedthere=Depuis combien de temps habitez-vous à cette adresse?

#GENERAL INFORMATION SECTION
driver.aboutyou.occupation=Quelle est votre occupation?
driver.aboutyou.fulltime.student = Êtes-vous étudiant à temps plein dans un collège ou une université en Alberta?
driver.aboutyou.universitydegree = Êtes-vous diplômé d'une université canadienne (bac, maîtrise ou doctorat)?
driver.aboutyou.start.this.insurance.coverage=Pour quelle date aimeriez-vous que votre assurance entre en vigueur?

#DRIVING RECORD SECTION
driver.aboutyou.agewhenlicenseobtained	= À quel âge avez-vous obtenu votre premier permis de conduire canadien?
driver.aboutyou.licensenumber			= N° de permis de conduire :
driver.aboutyou.licencetype				= Classe du permis de conduire valide
driver.aboutyou.trainingcourse          = Avez-vous complété avec succès un cours de conduite dans une école de conduite approuvée dans les trois dernières années ?

driver.aboutyou.claimsorlosses      = Au cours des dix dernières années, avez-vous fait des réclamations ou eu des accidents (responsables ou non) liés à la propriété ou à l'utilisation d'un véhicule automobile ?
driver.aboutyou.howlongagolossoccur = À quand remonte la réclamation ou l'accident?
driver.aboutyou.typeofloss			= Nature de l'accident ou de la réclamation
driver.aboutyou.loss.vehicle        = Véhicule impliqué dans l'accident / réclamation.

driver.aboutyou.commitedinfractions=Au cours des trois dernières années, avez-vous commis des infractions? (Ne pas tenir compte des contraventions de stationnement.) 
driver.aboutyou.conviction.date= Date de l'infraction
driver.aboutyou.nature.conviction=Type d'infraction

#INSURANCE HISTORY
driver.aboutyou.insured.in.canada 	 = Avez-vous déjà été titulaire d'une police d'assurance au Canada?
driver.aboutyou.age.first.insured 	 = À quel âge avez-vous obtenu votre premier contrat d'asssurance?
driver.aboutyou.interruptioncoverage = Votre couverture d'assurance a-t-elle été interrompue pour une période de plus de 2 ans au cours des 6 dernières années?
driver.aboutyou.outstanding.premiums.to.any.insurer=Avez-vous actuellement des primes d'assurance automobile impayées?
driver.aboutyou.currentinsurer=Qui est votre assureur actuel?

#******************************************************

#***************  OTHER DRIVERS  **********************

driver.aboutothers.occupation=Occupation
driver.aboutothers.universitydegree = Cette personne est-elle diplômée d'une université canadienne (bac, maîtrise ou doctorat)?
driver.aboutothers.fulltime.student = Ce conducteur est-il un étudiant à temps plein dans un collège ou une université en Alberta?

#DRIVING RECORD AND CLAIMS OR LOSSES
driver.aboutothers.licencetype			  = Classe du permis de conduire valide
driver.aboutothers.agewhenlicenseobtained = À quel âge ce conducteur a-t-il obtenu son premier permis de conduire canadien?
driver.aboutothers.trainingcourse         = Cette personne a-t-elle réussi un cours de conduite dans une école de conduite approuvée dans les trois dernières années ?

driver.aboutothers.claimsorlosses		= Au cours des dix dernières années, cette personne a-t-elle fait des réclamations ou eu des accidents (responsables ou non, déclarés ou non) liés à la propriété ou à l'utilisation d'un véhicule automobile ?
driver.aboutothers.howlongagolossoccur  = À quand remonte la réclamation ou l'accident?
driver.aboutothers.claimamount			= Montant total de la réclamation
driver.aboutothers.typeofloss			= Nature de l'accident ou de la réclamation
driver.aboutothers.loss.vehicle        = Véhicule impliqué dans l'accident / réclamation.

driver.aboutothers.commitedinfractions=Au cours des trois dernières années, cette personne a-t-elle commis des infractions? (Ne pas tenir compte des contraventions de stationnement.) 
driver.aboutothers.conviction.date=Date de l'infraction
driver.aboutothers.nature.conviction=Type d'infraction

#Insurance History
driver.aboutothers.insured.in.canada = Ce conducteur a-t-il déjà été titulaire d'une police d'assurance au Canada?
driver.aboutothers.age.first.insured = À quel âge ce conducteur a-t-il obtenu son premier contrat d'assurance?
driver.aboutothers.interruptioncoverage = La couverture d'assurance de cette personne a-t-elle été interrompue pour une période de plus de 2 ans au cours des 6 dernières années?
driver.aboutothers.outstanding.premiums.to.any.insurer=Ce conducteur a-t-il actuellement des primes d'assurance automobile impayées?

#************************************************

#Previous address
driver.postalcode=Code postal
driver.civicnumber=Numéro
driver.streetname=Nom de la rue
driver.apartmentnumber=N<span class='superscript'>o</span> d'appartement (si applicable)
driver.city=Ville
driver.province=Province
driver.country=Pays

driver.last.move.000=Moins de 6 mois 
driver.last.move.006=Entre 6 mois et 2 ans 
driver.last.move.024=Plus de 2 ans

driver.last.move.I00 = Moins de 1 an
driver.last.move.I12 = 1 an, mais moins de 2
driver.last.move.I24 = 2 ans, mais moins de 3
driver.last.move.I36 = 3 ans, mais moins que 4
driver.last.move.I48 = 4 ans, mais moins que 5
driver.last.move.A60 = 5 ans, mais moins que 6 
driver.last.move.A72 = 6 ans, mais moins que 7 
driver.last.move.A84 = 7 ans, mais moins que 8
driver.last.move.A96 = 8 ans, mais moins que 9
driver.last.move.108 = 9 ans, mais moins que 10
driver.last.move.120 = Plus de 10 ans

# ENUM TYPE DESCRIPTION - VALUE DOMAIN

driver.license.P=Permis régulier ou probatoire
driver.license.R=Permis régulier ou probatoire
driver.license.L=Permis d'apprenti conducteur
driver.license.U=Sans permis
driver.license.A=D'une autre province ou pays

driver.license.class.1=Classe 1 (Professionnel - tous véhicules)
driver.license.class.2=Classe 2 (Professionnel - autobus)
driver.license.class.3=Classe 3 (3-essieus ou plus)
driver.license.class.4=Classe 4 (Professionnel - taxi, ambulance)
driver.license.class.5=Classe 5 (2-essieus - autos, camions léger, autocaravanes ou cyclomoteurs)
driver.license.class.7=Classe 7 (Apprenti - 2-essieus, motorcyclette et cyclomoteurs)
driver.license.class.U=Aucun permis de conduire
driver.license.class.A=Permis d'une autre province / un autre pays

driver.claim.year.00=Moins de 1 an
driver.claim.year.01=1 an, mais moins de 2 
driver.claim.year.02=2 ans, mais moins de 3
driver.claim.year.03=3 ans, mais moins de 4
driver.claim.year.04=4 ans, mais moins de 5
driver.claim.year.05=5 ans, mais moins de 6
driver.claim.year.06=6 ans, mais moins de 7,
driver.claim.year.07=7 ans, mais moins de 10


driver.claim.nature.SOV=Vous avez heurté un objet ou un véhicule
driver.claim.nature.HDS=Vous étiez heurté par un conducteur désobéissant à un panneau de signalisation
driver.claim.nature.SP=Vous avez heurté un piéton
driver.claim.nature.SPS=Vous avez heurte un piéton désobéissant à un panneau de signalisation
driver.claim.nature.SB=Vous avez heurté un cycliste
driver.claim.nature.SBS=Vous avez heurté un cycliste désobéissant à un panneau de signalisation
driver.claim.nature.YRV=Vous avez heurté un véhicule par l'arrière
driver.claim.nature.YWR=Vous avez été heurté par un véhicule par l'arrière
driver.claim.nature.BAV=Vous avez reculé dans un autre véhicule
driver.claim.nature.VBY=Un autre véhicule a reculé dans votre véhicule
driver.claim.nature.SCA=Accident véhicule unique
driver.claim.nature.WMA=Vous étiez dans un accident multi-véhicule
driver.claim.nature.OAF=Autre accident responsable
driver.claim.nature.OPA=Autre accident partiellement responsable
driver.claim.nature.ONF=Autre accident non responsable
driver.claim.nature.IA=Vous avez heurté un oiseau / animal
driver.claim.nature.GW=Remplacement pare-brise / vitre
driver.claim.nature.GR=Réparation pare-brise / vitre
driver.claim.nature.ONC=Autres réclamation non collision

driver.marital.status.M=En couple
driver.marital.status.C=Célibataire

driver.month.0=Janvier
driver.month.1=Février
driver.month.2=Mars
driver.month.3=Avril
driver.month.4=Mai
driver.month.5=Juin
driver.month.6=Juillet
driver.month.7=Août
driver.month.8=Septembre
driver.month.9=Octobre
driver.month.10=Novembre
driver.month.11=Décembre

driver.conviction.type.CN=Négligence criminelle ou homicide involontaire
driver.conviction.type.DD=Conduite dangeureuse
driver.conviction.type.DUS=Conduite avec un permis suspendu
driver.conviction.type.CD=Conduite négligente
driver.conviction.type.IMP=Infraction reliée à la drogue ou à l'alcool
driver.conviction.type.FTY=Omission de céder la route
driver.conviction.type.TS=Omission de se conformer à la signalisation routière
driver.conviction.type.IP=Dépassement non sécuritaire
driver.conviction.type.FTSSA=Omission de rester sur les lieux d'un accident
driver.conviction.type.FRA=Omission de rapporter un accident ou des dommages
driver.conviction.type.FTSPO=Omission de s'arrêter pour la police
driver.conviction.type.PSB=Omission de s'arrêter pour un autobus
driver.conviction.type.TFTC=Omission de respecter une distance sécuritaire (talonnage)
driver.conviction.type.IPSB=Dépassement illégal d'un autobus scolaire
driver.conviction.type.RAC=Course
driver.conviction.type.FTS=Infraction reliée aux signalements
driver.conviction.type.PSG=Dépassement ou excès de vitesse en zone scolaire
driver.conviction.type.SP1=Excès de vitesse
driver.conviction.type.STN=Manoeuvre dangereuse

ONE = 1
TWO_OR_MORE = 2 ou plus
