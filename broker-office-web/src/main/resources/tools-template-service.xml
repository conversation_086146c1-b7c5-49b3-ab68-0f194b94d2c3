<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:jee="http://www.springframework.org/schema/jee"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context.xsd
                           http://www.springframework.org/schema/jee
                           http://www.springframework.org/schema/jee/spring-jee.xsd">
                           
	<bean class="com.intact.tools.template.service.jexl.JEXLTemplateApplicationService">
	
		<property name="templates">
			<map>        
				<entry key="ERROR"  value="&#34;The user &#34; + param1.getUserId() + &#34; is returning the following errors(s) &#34; + param1 + &#34; to the client.&#34;" />
				<entry key="RESPONSE_TIME" value="param1 + &#34;,&#34; + param2" />
				<entry key="MultiThreadQuoteBusinessProcess.searchQuotes" value="&#34;The user &#34; + param1.getUser() + &#34; is searching for quotes with search &#34;  + param2 + &#34;.&#34;" />
				<entry key="MultiThreadQuoteBusinessProcess.viewQuote" value="&#34;The user &#34; + param1.getUser() + &#34; is viewing the quote &#34;  + param2 + &#34;.&#34;" />
				<entry key="MultiThreadQuoteBusinessProcess.updateQuote" value="&#34;The user &#34; + param1.getUser() + &#34; is updating the quote &#34;  + param2 + &#34;.&#34;" />
				<entry key="MultiThreadQuoteBusinessProcess.uploadQuote" value="&#34;The user &#34; + param1.getUser() + &#34; is uploading the quote &#34;  + param2 + &#34;.&#34;" />
				<entry key="MultiThreadQuoteBusinessProcess.reassignQuotes" value="&#34;The user &#34; + param1.getUser() + &#34; is reassigning quotes &#34;  + param2 + &#34; to the broker &#34;  + param3 + &#34;.&#34;" />
				<entry key="BrokerService.listQuotes" value="&#34;The user &#34; + param1.getUser() + &#34; is listing quotes using the user context &#34;  + param1 + &#34;.&#34;" />
				<entry key="BrokerService.searchQuotes" value="&#34;The user &#34; + param1.getUser() + &#34; is searching quotes using the user context &#34;  + param1 + &#34; and the search &#34; + param2 + &#34;.&#34;" />
			</map>
		</property>
	</bean>
	
</beans>