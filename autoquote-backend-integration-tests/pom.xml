<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>intact.web.autoquote-backend</groupId>
        <artifactId>autoquote-backend</artifactId>
        <version>1.22.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>autoquote-backend-integration-tests</artifactId>

    <properties>
        <clueCumber.version>2.9.4</clueCumber.version>
        <cucumber-qaa-libs.version>6.1.36</cucumber-qaa-libs.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.trivago.rta</groupId>
            <artifactId>cluecumber-report-plugin</artifactId>
            <version>${clueCumber.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>intact.test</groupId>
            <artifactId>cucumber-base</artifactId>
            <version>${cucumber-qaa-libs.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>intact.test</groupId>
            <artifactId>cucumber-integration</artifactId>
            <version>${cucumber-qaa-libs.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>intact.test</groupId>
            <artifactId>cucumber-plugins</artifactId>
            <version>${cucumber-qaa-libs.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy-xml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>backend-integration-tests</id>
            <activation>
                <property>
                    <name>test-e2e-environment</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration>
                            <!-- We want to keep only integration tests -->
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                    <plugin>
                        <artifactId>maven-failsafe-plugin</artifactId>
                        <version>3.5.3</version>
                        <configuration>
                            <testFailureIgnore>false</testFailureIgnore>
                            <additionalClasspathElements>
                                <additionalClasspathElement>${basedir}/target/classes/config</additionalClasspathElement>
                            </additionalClasspathElements>
                        </configuration>
                        <executions>
                            <execution>
                                <id>integration-test</id>
                                <phase>integration-test</phase>
                                <goals>
                                    <goal>integration-test</goal>
                                    <goal>verify</goal>
                                </goals>
                                <configuration>
                                    <properties>
                                        <property>
                                            <name>dataproviderthreadcount</name>
                                            <value>5</value>
                                        </property>
                                    </properties>
                                    <includes>
                                        <include>**/RunCucumberIntegration.java</include>
                                    </includes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>com.trivago.rta</groupId>
                        <artifactId>cluecumber-report-plugin</artifactId>
                        <version>${clueCumber.version}</version>
                        <executions>
                            <execution>
                                <id>report</id>
                                <phase>post-integration-test</phase>
                                <goals>
                                    <goal>reporting</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <sourceJsonReportDirectory>${project.build.directory}/cucumber-report</sourceJsonReportDirectory>
                            <generatedHtmlReportDirectory>${project.build.directory}/generated-report</generatedHtmlReportDirectory>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
