package intact.test.template.integration.api;


import intact.test.integration.context.RequestWrapper;
import intact.test.integration.models.api.AbstractAPI;
import intact.test.template.integration.api.formatters.GreetingFormatter;
import intact.test.template.integration.context.TestContext;
import intact.test.template.integration.models.RequestDTO;
import io.restassured.http.Method;
import io.restassured.specification.RequestSpecification;

public class Greeting<PERSON>I extends AbstractAPI<TestContext> {

  private final String DEMO_URL = context.getEnvironmentUtils().getEnvProperty("service.url");

  public GreetingAPI(TestContext context) {
    super(context);
  }

  public RequestWrapper validRequest() {
    context.getCurrentRequestWrapper().addFormatter(new GreetingFormatter());

    String name = ((RequestDTO) context.getCurrentRequestWrapper().getRequestDTO()).getData();
    RequestSpecification request;
    if (name == null) {
      request = newRequest().baseUri(DEMO_URL).basePath("/greeting").header("Connection", "keep-alive");
    } else {
      request = newRequest().baseUri(DEMO_URL).basePath("/greeting").header("Connection", "keep-alive")
                      .queryParam("name", name);
    }

    return updateRequestWrapper(request, Method.GET);
  }
}
