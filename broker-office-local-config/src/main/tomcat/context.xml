<Context>
	<!-- Define datasources (should match JNDI name for lookup) -->
	<Resource
			name="jdbc/plpolicy"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="50"
			maxIdle="0"
			maxWaitMillis="180000"
			username="PLP_WZONE_INT"
			password="BNrx=k3M87b"
			driverClassName="oracle.jdbc.OracleDriver"
			url="*****************************************************************"/>

	<Resource
			name="jdbc/brm"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="25"
			maxIdle="0"
			maxWaitMillis="180000"
			username="brmadmin"
			password="kanAd$Yc3ia"
			driverClassName="oracle.jdbc.OracleDriver"
			url="***************************************************"/>

	<Resource
			name="jdbc/CIF"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="25"
			maxIdle="0"
			maxWaitMillis="180000"
			username="CIF_WZONE_INT"
			password="hr1rWT-7GOda1"
			driverClassName="oracle.jdbc.OracleDriver"
			url="*****************************************************************"/>

	<Resource
			name="jdbc/SAD"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="10"
			maxIdle="1"
			maxWaitMillis="180000"
			username="sadusrrw"
			password="93E220_6CA8"
			driverClassName="oracle.jdbc.OracleDriver"
			url="*******************************************************"/>

	<Resource
			name="jdbc/dw"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="25"
			maxIdle="0"
			maxWaitMillis="180000"
			username="dw_report_intact1"
			password="tra23por"
			driverClassName="oracle.jdbc.OracleDriver"
			url="*******************************************************"/>

	<!-- Add SameSite to all cookies sent by the server -->
	<CookieProcessor sameSiteCookies="strict"/>
</Context>
