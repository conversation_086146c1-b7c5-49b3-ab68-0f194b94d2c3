# Actuator 404 Error Troubleshooting

## Issue: Getting 404 errors when accessing actuator endpoints

### Step-by-Step Diagnostic

#### 1. Test Basic Servlet Functionality
First, test if the servlet mechanism is working at all:

```bash
curl http://localhost:8080/webzone/simple-test
```

**Expected Response:**
```
Simple Test Servlet is working!
Request URI: /webzone/simple-test
Context Path: /webzone
Servlet Path: /simple-test
Path Info: null
```

**If this fails (404):**
- The servlet classes are not compiled/deployed
- The application is not deployed properly
- The context path might be different

#### 2. Check Application Deployment

**Verify the application is deployed:**
1. Check Tomcat manager or logs
2. Verify the WAR file contains the servlet classes
3. Check if the application starts without errors

**Check WAR file contents:**
```bash
# If you have the WAR file
jar -tf webzone.war | grep ActuatorHealthServlet
```

Should show:
```
WEB-INF/classes/com/intact/brokeroffice/actuator/servlet/ActuatorHealthServlet.class
```

#### 3. Check Tomcat Logs

Look for servlet initialization errors in Tom<PERSON> logs:
```bash
# Check catalina.out or application logs
tail -f $TOMCAT_HOME/logs/catalina.out
```

Look for:
- `ClassNotFoundException` for servlet classes
- Servlet initialization errors
- Spring context loading errors

#### 4. Verify Context Path

The URL structure should be:
```
http://localhost:8080/{context-path}/{servlet-mapping}
```

If your application is deployed with a different context path, adjust accordingly:
```bash
# If context path is different, try:
curl http://localhost:8080/{actual-context}/actuator/health
```

#### 5. Check Servlet Compilation

Ensure the servlet classes are compiled and in the correct package:

**Expected file structure in WAR:**
```
WEB-INF/
  classes/
    com/
      intact/
        brokeroffice/
          actuator/
            servlet/
              ActuatorHealthServlet.class
              ActuatorInfoServlet.class
              ActuatorMetricsServlet.class
              SimpleTestServlet.class
```

### Common Issues and Solutions

#### Issue 1: Servlets Not Compiled
**Symptoms:** 404 errors for all servlet endpoints
**Solution:** 
1. Clean and rebuild the project
2. Verify Maven/build tool is including the new servlet classes
3. Check if the servlet package is being excluded by build configuration

#### Issue 2: Wrong Context Path
**Symptoms:** 404 errors, but application works for JSF pages
**Solution:**
1. Check the actual context path by accessing a working JSF page
2. Use the correct context path in curl commands
3. Check server.xml or deployment configuration

#### Issue 3: Servlet Initialization Failure
**Symptoms:** 404 errors with servlet initialization errors in logs
**Solution:**
1. Check for missing dependencies (Jackson, Spring)
2. Verify Spring context is loading properly
3. Check for classpath issues

#### Issue 4: Security Filter Blocking
**Symptoms:** 403 or 404 errors specifically for actuator endpoints
**Solution:**
1. Check if `ActuatorSecurityFilter` is blocking requests
2. Temporarily disable the security filter to test
3. Verify filter configuration in web.xml

### Quick Fixes to Try

#### Fix 1: Simplify web.xml Configuration
Remove complex servlet mappings and use simple ones:

```xml
<servlet-mapping>
    <servlet-name>actuatorHealthServlet</servlet-name>
    <url-pattern>/health</url-pattern>
</servlet-mapping>
```

Then test: `curl http://localhost:8080/webzone/health`

#### Fix 2: Use Annotation-Based Servlets
Add `@WebServlet` annotation to servlets:

```java
@WebServlet("/actuator/health")
public class ActuatorHealthServlet extends HttpServlet {
    // ...
}
```

#### Fix 3: Check for Conflicting URL Patterns
Ensure no other servlets or filters are intercepting the `/actuator/*` pattern.

### Debugging Commands

```bash
# Test simple servlet first
curl -v http://localhost:8080/webzone/simple-test

# Test with different context paths
curl -v http://localhost:8080/actuator/health
curl -v http://localhost:8080/webzone/actuator/health
curl -v http://localhost:8080/broker-office-web/actuator/health

# Check if any actuator endpoint works
curl -v http://localhost:8080/webzone/actuator/test
curl -v http://localhost:8080/webzone/actuator/info
curl -v http://localhost:8080/webzone/actuator/metrics

# Test with simplified paths
curl -v http://localhost:8080/webzone/health
curl -v http://localhost:8080/webzone/info
```

### Next Steps

1. **Start with the simple test servlet** - if this doesn't work, the issue is with basic servlet deployment
2. **Check Tomcat logs** for any initialization errors
3. **Verify the correct context path** by testing existing JSF pages
4. **Rebuild and redeploy** the application to ensure servlet classes are included
5. **Try simplified servlet mappings** without the `/actuator` prefix

Once the simple servlet works, the actuator servlets should work too.
