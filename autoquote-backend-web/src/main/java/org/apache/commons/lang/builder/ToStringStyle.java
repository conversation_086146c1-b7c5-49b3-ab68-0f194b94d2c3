package org.apache.commons.lang.builder;

/**
 * Bridge class to provide compatibility with commons-lang ToStringStyle
 * while using commons-lang3 internally.
 * 
 * This class extends the commons-lang3 ToStringStyle to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 */
public class ToStringStyle extends org.apache.commons.lang3.builder.ToStringStyle {

    private static final long serialVersionUID = 1L;

    /**
     * The default toString style. Using the Person example from ToStringBuilder,
     * the output would look like this:
     * 
     * <pre>
     * Person@182f0db[name=<PERSON>,age=33,smoker=false]
     * </pre>
     */
    public static final ToStringStyle DEFAULT_STYLE = new ToStringStyle() {
        private static final long serialVersionUID = 1L;
        {
            this.setUseClassName(true);
            this.setUseIdentityHashCode(true);
            this.setUseFieldNames(true);
            this.setContentStart("[");
            this.setContentEnd("]");
            this.setFieldNameValueSeparator("=");
            this.setFieldSeparator(",");
        }
    };

    /**
     * The multi line toString style. Using the Person example from ToStringBuilder,
     * the output would look like this:
     * 
     * <pre>
     * Person@182f0db[
     *   name=John Doe
     *   age=33
     *   smoker=false
     * ]
     * </pre>
     */
    public static final ToStringStyle MULTI_LINE_STYLE = new ToStringStyle() {
        private static final long serialVersionUID = 1L;
        {
            this.setUseClassName(true);
            this.setUseIdentityHashCode(true);
            this.setUseFieldNames(true);
            this.setContentStart("[");
            this.setFieldSeparator(System.lineSeparator() + "  ");
            this.setFieldSeparatorAtStart(true);
            this.setContentEnd(System.lineSeparator() + "]");
        }
    };

    /**
     * The no field names toString style. Using the Person example from ToStringBuilder,
     * the output would look like this:
     * 
     * <pre>
     * Person@182f0db[John Doe,33,false]
     * </pre>
     */
    public static final ToStringStyle NO_FIELD_NAMES_STYLE = new ToStringStyle() {
        private static final long serialVersionUID = 1L;
        {
            this.setUseClassName(true);
            this.setUseIdentityHashCode(true);
            this.setUseFieldNames(false);
            this.setContentStart("[");
            this.setContentEnd("]");
            this.setFieldSeparator(",");
        }
    };

    /**
     * The short prefix toString style. Using the Person example from ToStringBuilder,
     * the output would look like this:
     * 
     * <pre>
     * Person[name=John Doe,age=33,smoker=false]
     * </pre>
     */
    public static final ToStringStyle SHORT_PREFIX_STYLE = new ToStringStyle() {
        private static final long serialVersionUID = 1L;
        {
            this.setUseShortClassName(true);
            this.setUseIdentityHashCode(false);
            this.setUseFieldNames(true);
            this.setContentStart("[");
            this.setContentEnd("]");
            this.setFieldNameValueSeparator("=");
            this.setFieldSeparator(",");
        }
    };

    /**
     * The simple toString style. Using the Person example from ToStringBuilder,
     * the output would look like this:
     * 
     * <pre>
     * John Doe,33,false
     * </pre>
     */
    public static final ToStringStyle SIMPLE_STYLE = new ToStringStyle() {
        private static final long serialVersionUID = 1L;
        {
            this.setUseClassName(false);
            this.setUseIdentityHashCode(false);
            this.setUseFieldNames(false);
            this.setContentStart("");
            this.setContentEnd("");
            this.setFieldSeparator(",");
        }
    };

    /**
     * Constructor.
     */
    protected ToStringStyle() {
        super();
    }

    // Bridge methods to maintain compatibility with commons-lang API
    // Most functionality is inherited from commons-lang3 ToStringStyle

    /**
     * Gets the content start text.
     *
     * @return the current content start text
     */
    public String getContentStart() {
        return super.getContentStart();
    }

    /**
     * Sets the content start text.
     *
     * @param contentStart the new content start text
     */
    public void setContentStart(String contentStart) {
        super.setContentStart(contentStart);
    }

    /**
     * Gets the content end text.
     *
     * @return the current content end text
     */
    public String getContentEnd() {
        return super.getContentEnd();
    }

    /**
     * Sets the content end text.
     *
     * @param contentEnd the new content end text
     */
    public void setContentEnd(String contentEnd) {
        super.setContentEnd(contentEnd);
    }

    /**
     * Gets the field name value separator text.
     *
     * @return the current field name value separator text
     */
    public String getFieldNameValueSeparator() {
        return super.getFieldNameValueSeparator();
    }

    /**
     * Sets the field name value separator text.
     *
     * @param fieldNameValueSeparator the new field name value separator text
     */
    public void setFieldNameValueSeparator(String fieldNameValueSeparator) {
        super.setFieldNameValueSeparator(fieldNameValueSeparator);
    }

    /**
     * Gets the field separator text.
     *
     * @return the current field separator text
     */
    public String getFieldSeparator() {
        return super.getFieldSeparator();
    }

    /**
     * Sets the field separator text.
     *
     * @param fieldSeparator the new field separator text
     */
    public void setFieldSeparator(String fieldSeparator) {
        super.setFieldSeparator(fieldSeparator);
    }

    /**
     * Gets whether the field separator should be added at the start.
     *
     * @return the field separator flag
     */
    public boolean isFieldSeparatorAtStart() {
        return super.isFieldSeparatorAtStart();
    }

    /**
     * Sets whether the field separator should be added at the start.
     *
     * @param fieldSeparatorAtStart the field separator flag
     */
    public void setFieldSeparatorAtStart(boolean fieldSeparatorAtStart) {
        super.setFieldSeparatorAtStart(fieldSeparatorAtStart);
    }

    /**
     * Gets whether to use the class name.
     *
     * @return the current useClassName flag
     */
    public boolean isUseClassName() {
        return super.isUseClassName();
    }

    /**
     * Sets whether to use the class name.
     *
     * @param useClassName the new useClassName flag
     */
    public void setUseClassName(boolean useClassName) {
        super.setUseClassName(useClassName);
    }

    /**
     * Gets whether to use the short class name.
     *
     * @return the current useShortClassName flag
     */
    public boolean isUseShortClassName() {
        return super.isUseShortClassName();
    }

    /**
     * Sets whether to use the short class name.
     *
     * @param useShortClassName the new useShortClassName flag
     */
    public void setUseShortClassName(boolean useShortClassName) {
        super.setUseShortClassName(useShortClassName);
    }

    /**
     * Gets whether to use the identity hash code.
     *
     * @return the current useIdentityHashCode flag
     */
    public boolean isUseIdentityHashCode() {
        return super.isUseIdentityHashCode();
    }

    /**
     * Sets whether to use the identity hash code.
     *
     * @param useIdentityHashCode the new useIdentityHashCode flag
     */
    public void setUseIdentityHashCode(boolean useIdentityHashCode) {
        super.setUseIdentityHashCode(useIdentityHashCode);
    }

    /**
     * Gets whether to use the field names.
     *
     * @return the current useFieldNames flag
     */
    public boolean isUseFieldNames() {
        return super.isUseFieldNames();
    }

    /**
     * Sets whether to use the field names.
     *
     * @param useFieldNames the new useFieldNames flag
     */
    public void setUseFieldNames(boolean useFieldNames) {
        super.setUseFieldNames(useFieldNames);
    }
}
