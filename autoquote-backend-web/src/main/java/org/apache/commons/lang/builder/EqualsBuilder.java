package org.apache.commons.lang.builder;

/**
 * Bridge class to provide compatibility with commons-lang EqualsBuilder
 * while using commons-lang3 internally.
 * 
 * This class extends the commons-lang3 EqualsBuilder to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 */
public class EqualsBuilder extends org.apache.commons.lang3.builder.EqualsBuilder {

    /**
     * Constructs an EqualsBuilder.
     */
    public EqualsBuilder() {
        super();
    }

    // Override methods to return the bridge type for method chaining

    @Override
    public EqualsBuilder append(boolean lhs, boolean rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(boolean[] lhs, boolean[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(byte lhs, byte rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(byte[] lhs, byte[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(char lhs, char rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(char[] lhs, char[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(double lhs, double rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(double[] lhs, double[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(float lhs, float rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(float[] lhs, float[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(int lhs, int rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(int[] lhs, int[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(long lhs, long rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(long[] lhs, long[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(Object lhs, Object rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(Object[] lhs, Object[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(short lhs, short rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder append(short[] lhs, short[] rhs) {
        super.append(lhs, rhs);
        return this;
    }

    @Override
    public EqualsBuilder appendSuper(boolean superEquals) {
        super.appendSuper(superEquals);
        return this;
    }

    // Static utility methods for convenience

    /**
     * Uses reflection to determine if the two Objects are equal.
     *
     * @param lhs this object
     * @param rhs the other object
     * @return true if the two Objects are equal
     */
    public static boolean reflectionEquals(Object lhs, Object rhs) {
        return org.apache.commons.lang3.builder.EqualsBuilder.reflectionEquals(lhs, rhs);
    }

    /**
     * Uses reflection to determine if the two Objects are equal.
     *
     * @param lhs this object
     * @param rhs the other object
     * @param testTransients whether to include transient fields
     * @return true if the two Objects are equal
     */
    public static boolean reflectionEquals(Object lhs, Object rhs, boolean testTransients) {
        return org.apache.commons.lang3.builder.EqualsBuilder.reflectionEquals(lhs, rhs, testTransients);
    }

    /**
     * Uses reflection to determine if the two Objects are equal.
     *
     * @param lhs this object
     * @param rhs the other object
     * @param excludeFields array of field names to exclude from testing
     * @return true if the two Objects are equal
     */
    public static boolean reflectionEquals(Object lhs, Object rhs, String... excludeFields) {
        return org.apache.commons.lang3.builder.EqualsBuilder.reflectionEquals(lhs, rhs, excludeFields);
    }

    /**
     * Uses reflection to determine if the two Objects are equal.
     *
     * @param lhs this object
     * @param rhs the other object
     * @param testTransients whether to include transient fields
     * @param reflectUpToClass the superclass to reflect up to (inclusive), may be null
     * @return true if the two Objects are equal
     */
    public static boolean reflectionEquals(Object lhs, Object rhs, boolean testTransients, Class<?> reflectUpToClass) {
        return org.apache.commons.lang3.builder.EqualsBuilder.reflectionEquals(lhs, rhs, testTransients, reflectUpToClass);
    }

    /**
     * Uses reflection to determine if the two Objects are equal.
     *
     * @param lhs this object
     * @param rhs the other object
     * @param testTransients whether to include transient fields
     * @param reflectUpToClass the superclass to reflect up to (inclusive), may be null
     * @param excludeFields array of field names to exclude from testing
     * @return true if the two Objects are equal
     */
    public static boolean reflectionEquals(Object lhs, Object rhs, boolean testTransients, Class<?> reflectUpToClass, String... excludeFields) {
        return org.apache.commons.lang3.builder.EqualsBuilder.reflectionEquals(lhs, rhs, testTransients, reflectUpToClass, excludeFields);
    }
}
