package org.apache.commons.lang.builder;

/**
 * Bridge class to provide compatibility with commons-lang ToStringBuilder
 * while using commons-lang3 internally.
 * 
 * This class extends the commons-lang3 ToStringBuilder to maintain
 * compatibility with legacy code that expects the old commons-lang package.
 */
public class ToStringBuilder extends org.apache.commons.lang3.builder.ToStringBuilder {

    /**
     * Constructs a ToStringBuilder for the specified object.
     *
     * @param object the Object to build a toString for
     */
    public ToStringBuilder(Object object) {
        super(object);
    }

    /**
     * Constructs a ToStringBuilder for the specified object using the given style.
     *
     * @param object the Object to build a toString for
     * @param style the style of the toString to create
     */
    public ToStringBuilder(Object object, ToStringStyle style) {
        super(object, (org.apache.commons.lang3.builder.ToStringStyle) style);
    }

    /**
     * Constructs a ToStringBuilder for the specified object.
     *
     * @param object the Object to build a toString for
     * @param style the style of the toString to create
     * @param buffer the StringBuffer to populate
     */
    public ToStringBuilder(Object object, ToStringStyle style, StringBuffer buffer) {
        super(object, (org.apache.commons.lang3.builder.ToStringStyle) style, buffer);
    }

    // Override methods to return the bridge type for method chaining

    @Override
    public ToStringBuilder append(boolean value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(boolean[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(byte value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(byte[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(char value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(char[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(double value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(double[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(float value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(float[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(int value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(int[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(long value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(long[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(Object obj) {
        super.append(obj);
        return this;
    }

    @Override
    public ToStringBuilder append(Object[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(short value) {
        super.append(value);
        return this;
    }

    @Override
    public ToStringBuilder append(short[] array) {
        super.append(array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, boolean value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, boolean[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, boolean[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, byte value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, byte[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, byte[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, char value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, char[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, char[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, double value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, double[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, double[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, float value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, float[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, float[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, int value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, int[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, int[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, long value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, long[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, long[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, Object obj) {
        super.append(fieldName, obj);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, Object obj, boolean fullDetail) {
        super.append(fieldName, obj, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, Object[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, Object[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, short value) {
        super.append(fieldName, value);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, short[] array) {
        super.append(fieldName, array);
        return this;
    }

    @Override
    public ToStringBuilder append(String fieldName, short[] array, boolean fullDetail) {
        super.append(fieldName, array, fullDetail);
        return this;
    }

    @Override
    public ToStringBuilder appendAsObjectToString(Object object) {
        super.appendAsObjectToString(object);
        return this;
    }

    @Override
    public ToStringBuilder appendSuper(String superToString) {
        super.appendSuper(superToString);
        return this;
    }

    @Override
    public ToStringBuilder appendToString(String toString) {
        super.appendToString(toString);
        return this;
    }

    // Static utility methods for convenience

    /**
     * Uses reflection to build a toString for the specified object.
     *
     * @param object the Object to be output
     * @return the String result
     */
    public static String reflectionToString(Object object) {
        return org.apache.commons.lang3.builder.ToStringBuilder.reflectionToString(object);
    }

    /**
     * Uses reflection to build a toString for the specified object.
     *
     * @param object the Object to be output
     * @param style the style of the toString to create
     * @return the String result
     */
    public static String reflectionToString(Object object, ToStringStyle style) {
        return org.apache.commons.lang3.builder.ToStringBuilder.reflectionToString(object, (org.apache.commons.lang3.builder.ToStringStyle) style);
    }

    /**
     * Uses reflection to build a toString for the specified object.
     *
     * @param object the Object to be output
     * @param style the style of the toString to create
     * @param outputTransients whether to include transient fields
     * @return the String result
     */
    public static String reflectionToString(Object object, ToStringStyle style, boolean outputTransients) {
        return org.apache.commons.lang3.builder.ToStringBuilder.reflectionToString(object, (org.apache.commons.lang3.builder.ToStringStyle) style, outputTransients);
    }

    /**
     * Uses reflection to build a toString for the specified object.
     *
     * @param object the Object to be output
     * @param style the style of the toString to create
     * @param outputTransients whether to include transient fields
     * @param reflectUpToClass the superclass to reflect up to (inclusive), may be null
     * @return the String result
     */
    @SuppressWarnings("unchecked")
    public static String reflectionToString(Object object, ToStringStyle style, boolean outputTransients, Class<?> reflectUpToClass) {
        return org.apache.commons.lang3.builder.ToStringBuilder.reflectionToString(object, (org.apache.commons.lang3.builder.ToStringStyle) style, outputTransients, (Class) reflectUpToClass);
    }
}
