package intact.lab.autoquote.backend.services.impl;

/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */


import intact.lab.autoquote.backend.services.IValueDomainService;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Proxy;


/**
 * Values Domains FactoryBean. Return a proxy handle by ValueDomainInvocationHandler.
 *
 * <AUTHOR> pabonnea
 */
@Component("valueDomainService")
public class ValueDomainServiceFactory implements FactoryBean<Object> {

	private final InvocationHandler domainInvocationHandler;

	public ValueDomainServiceFactory(@Qualifier("ValueDomainInvocationHandler") InvocationHandler domainInvocationHandler) {
		this.domainInvocationHandler = domainInvocationHandler;
	}

	/**
	 * @see FactoryBean#getObject()
	 */
	public Object getObject() throws Exception {
		return Proxy.newProxyInstance(IValueDomainService.class.getClassLoader(), new Class[]{IValueDomainService.class}, this.domainInvocationHandler);
	}

	/**
	 * @see FactoryBean#getObjectType()
	 */
	public Class<IValueDomainService> getObjectType() {
		return IValueDomainService.class;
	}

	/**
	 * @see FactoryBean#isSingleton()
	 */
	public boolean isSingleton() {
		return true;
	}
}
