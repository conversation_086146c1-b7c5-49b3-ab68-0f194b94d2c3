package intact.lab.autoquote.backend.services.rating;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import org.owasp.esapi.Logger;

import java.io.OutputStream;

public interface IRatingServiceHelper {

	/**
	 * @deprecated use signature using Logger instead of direct OutputStream
	 */
	@Deprecated
	void printPolicyCoverage(PolicyVersion policyVersion, boolean beforeDeviation, OutputStream output, final OfferTypeCodeEnum aReferenceOfferType);
	
	void printPolicyCoverage(PolicyVersion policyVersion, boolean beforeDeviation, final Logger log, final OfferTypeCodeEnum aReferenceOfferType);
	
	void printFactors(PolicyVersion policyVersion, final Logger log);
}
