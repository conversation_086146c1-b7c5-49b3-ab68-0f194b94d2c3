/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.mediation.impl;

import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.party.PartyGroupType;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.driver.DriverLicenseClass;
import com.ing.canada.plp.domain.enums.DriverLicenseClassCodeEnum;
import com.ing.canada.plp.domain.enums.GroupRepositoryEntryEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.helper.IInsuranceRiskHelper;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.IVehicleHelper;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.common.datamediator.com.plp.IMediatorAdvisor;
import com.intact.common.datamediator.com.plp.IMediatorClaimPlp;
import com.intact.common.datamediator.com.plp.IMediatorComPlp;
import com.intact.common.datamediator.com.plp.IMediatorDriverPlp;
import com.intact.common.datamediator.com.plp.IMediatorVehiclePlp;
import com.intact.common.datamediator.com.plp.impl.MediatorPaymentPlp;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.apache.commons.lang3.BooleanUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Locale;
import java.util.Map;

/**
 * <AUTHOR> L.
 *
 */
public abstract class COMtoPLAdapterIntact extends COMtoPLAdapter {

	private static final Logger LOG = ESAPI.getLogger(COMtoPLAdapterIntact.class);

	private static String INSURED_GROUP_UNIVERSITY_CODE = "Uni";

	public COMtoPLAdapterIntact(ICommonBusinessProcess commonBusinessProcess, IMediatorComPlp mediatorComPlp,
								IMediatorVehiclePlp mediatorVehiclePlp, IMediatorDriverPlp mediatorDriverPlp,
								IMediatorClaimPlp mediatorClaim, MediatorPaymentPlp mediatorPaymentPlp,
								IVehicleHelper vehicleHelper, IPartyHelper partyHelper, IMediatorAdvisor mediatorAdvisor,
								IPolicyVersionHelper plpPolicyVersionHelper, IInsuranceRiskHelper plpInsuranceRiskHelper,
								IDateManagerService capiPolicyChangeDateService) {
		super(commonBusinessProcess, mediatorComPlp, mediatorVehiclePlp, mediatorDriverPlp, mediatorClaim, mediatorPaymentPlp,
				vehicleHelper, partyHelper, mediatorAdvisor, plpPolicyVersionHelper, plpInsuranceRiskHelper,
				capiPolicyChangeDateService);
	}

	@Override
	protected void localizedPostMediationForParty(ComEvent event, Party party, Map<String, String> insuredGroups, ManufacturingContext context, Locale locale) {
		// nothing in common code
	}

	@Override
	protected void localizedPostMediationForVehicle(Vehicle vehicle, ManufacturingContext context, Locale locale) {
		// nothing in common code
	}

	@Override
	protected void localizedDriversLicenseJurisdiction(Party party) {
		// nothing in common code
	}

	/**
	 * {@inheritDoc}
	 */
	protected void addSpecificInsuredGroups(final Party party, final ManufacturingContext context, Map<String, String> insuredGroups) {
		String insuredGroupUni = null;
		if (BooleanUtils.isTrue(party.getHolderOfDiplomaFromCanadianUniversity())) {
			insuredGroupUni = this.driverBusinessProcess.getInsuredGroupFromService(Province.fromDbCode(context.getProvince()), PartyGroupType.UNIVERSITY,
					GroupRepositoryEntryEnum.UNIVERSITY.getCode(), context, null);
		}

		insuredGroups.put(INSURED_GROUP_UNIVERSITY_CODE, insuredGroupUni);
	}

	/**
	 * Set driver license class for Ontario, based on the driver license type.
	 *
	 * @param party the {@link Party}
	 */
	protected static void setDriverLicenseClass(Party party) {
		Assert.notNull(party, "Party parameter cannot be null.");
		Assert.notNull(party.getDriverComplementInfo(), "Party.driverComplementInfo cannot be null.");
		Assert.notNull(party.getDriverComplementInfo().getDriverLicenseType(), "Party.driverComplementInfo.driverLicenseType cannot be null.");

		DriverComplementInfo driver = party.getDriverComplementInfo();

		// clear existing license class and recreate them
		driver.clearDriverLicenseClass();

		DriverLicenseClassCodeEnum licClassCode = null;
		switch (driver.getDriverLicenseType()) {
		case LEARNER_LICENSE:
			licClassCode = DriverLicenseClassCodeEnum.LEARNER_PERMIT;
			break;
		case NO_LICENSE:
			// no class to set when no license
			break;
		case PROBATIONARY_LICENSE:
			licClassCode = DriverLicenseClassCodeEnum.PROBATIONARY_PERMIT;
			break;
		case REGULAR_LICENSE:
			licClassCode = DriverLicenseClassCodeEnum.GRADUATED_PERMIT;
			break;
		case VALID_LICENSE_ISSUED_IN_ANOTHER_PROVINCE_COUNTRY:
			// no class to set when no licensed in another province/country
			break;
		default:
			if (LOG.isErrorEnabled()) {
				LOG.error(Logger.EVENT_SUCCESS, String.format(">> unable to map licenseType to licenseClass for type='%s'", driver.getDriverLicenseType()).toString());
			}
		}

		if (LOG.isDebugEnabled()) {
			LOG.debug(Logger.EVENT_SUCCESS, String.format(">> set licenseClassCode='%s' for licenseType='%s'", licClassCode != null ? licClassCode.getCode() : "null", driver.getDriverLicenseType()));
		}

		// DE62 - DE2228
		if (licClassCode != null) { // only set license class if it exists
			DriverLicenseClass newLicenseClass = new DriverLicenseClass();
			newLicenseClass.setDriverLicenseClass(licClassCode);
			newLicenseClass.setEffectiveDate(new Date());
			driver.addDriverLicenseClass(newLicenseClass);
		}
	}
}
