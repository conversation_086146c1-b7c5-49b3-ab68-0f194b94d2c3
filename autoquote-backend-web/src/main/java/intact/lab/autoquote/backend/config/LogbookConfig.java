package intact.lab.autoquote.backend.config;

import com.intact.logbook.core.Logbook;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(value = "logging.logbook.enabled", havingValue = "true")
public class LogbookConfig {

    public static final String APP_NAME = "Autoquote-Backend";

    @Bean
    public Logbook logbook() {
        return Logbook.builder(APP_NAME).build();
    }
}
