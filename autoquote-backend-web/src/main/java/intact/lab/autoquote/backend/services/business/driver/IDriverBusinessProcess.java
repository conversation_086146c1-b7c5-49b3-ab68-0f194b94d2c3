package intact.lab.autoquote.backend.services.business.driver;

import com.ing.canada.cif.domain.IPostalCode;
import com.ing.canada.common.domain.Municipality;
import com.ing.canada.common.domain.ValidValueBO;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.party.PartyGroupType;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.driver.DriverComplementInfo;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.DistributorCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.Claim;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import com.intact.business.rules.exception.RuleExceptionResult;
import intact.lab.autoquote.backend.common.exception.SingleIdActiveProductException;

import java.util.List;
import java.util.Locale;
import java.util.Map;

public interface IDriverBusinessProcess {

    /**
     * Get the count of drivers' associated to a policy version.<br>
     * Requires that the policy version be already populated in memory.
     *
     * @param aPolicyVersion - {@link PolicyVersion}
     * @return the number of drivers linked to the policyVersion
     */
    int getDriversCount(final PolicyVersion aPolicyVersion);

    /**
     * returns the province matching a municipality.
     *
     * @param aMunicipality the a municipality
     * @param postalCode the postal code
     * @param context the context
     *
     * @return the province for municipality
     */
    ProvinceCodeEnum getProvinceForMunicipality(Municipality aMunicipality, String postalCode, ManufacturingContext context);

    /**
     * Returns a list of Municipality instances rather than a list of ValidValueBO instances.
     *
     * @param locale the locale
     * @param postalCode the postal code
     * @param context the context
     * @return the municipalities from postal code
     */
    List<Municipality> getMunicipalitiesFromPostalCode(Locale locale, String postalCode, ManufacturingContext context);

    /**
     * Gets the postal code info.
     *
     * @param locale the locale
     * @param postalCode the postal code
     * @return the postal code info
     */
    IPostalCode getPostalCodeInfo(final Locale locale, final String postalCode);

    /**
     * Gets the province for a given postal code.
     *
     * @param postalCode - {@link String} value of a postal code
     * @return {@link ProvinceCodeEnum}
     */
    ProvinceCodeEnum getProvince(String postalCode);

    /**
     * Gets valieValue List of cities for a given postal code.
     *
     * @param postalCode - {@link String} value of a postal code
     * @return {@link List}<{@link ValidValueBO}>
     */
    List<ValidValueBO> getCitiesValidValues(String postalCode);

    /**
     * Gets the cities for a given postal code.
     *
     * @param postalCode - {@link String} postal code
     * @return list of cities matching the postal code
     */
    List<String> getCities(String postalCode);

    /**
     * Set the municipality from a party/driver.
     *
     * @param policyVersion - the {@link PolicyVersion}
     * @param party - the driver's {@link Party} to complete the municipality
     * @param context - the {@link ManufacturingContext}
     * @param locale - the {@link Locale}
     */
    void setModelForMunicipality(PolicyVersion policyVersion, Party party, ManufacturingContext context, Locale locale);

    /**
     * Manage MunicipalityRepositoryEntry.
     *
     * @param aLocale - {@link Locale}
     * @param address - {@link Address}
     * @param postalCode - {@link String} postal code
     * @param context - {@link ManufacturingContext}
     */
    void manageMunicipalityRepositoryEntry(Locale aLocale, Address address, String postalCode, ManufacturingContext context);

    /**
     * Set the affinity group fpr a party/driver.
     *
     * @param policyVersion - {@link PolicyVersion}
     * @param party - {@link DriverComplementInfo}
     * @param context - {@link ManufacturingContext}
     * @param insuredGroups - {@link Map}<{@link String},{@link String}>
     */
    void setModelForAffinityGroup(PolicyVersion policyVersion, Party party, ManufacturingContext context,
                                  Map<String, String> insuredGroups);

    /**
     * Set kind of loss on claim.
     *
     * @param aClaim {@link Claim}
     * @param claimNatureAmount {@link String}
     * @return {@link Claim}
     */
    Claim setClaimKindOfLoss(Claim aClaim, String claimNatureAmount);

    /**
     * Remove a driver from policy version and remove the policy version's relationships. <br>
     *
     * => This method does not directly persist. <=<br>
     * It only removes the relationships from a driver to its linked policyVersion
     *
     * @param aDriver - {@link DriverComplementInfo}
     * @param aPolicyVersion - {@link PolicyVersion}
     * @param application - {@link ApplicationEnum}
     * @throws AccessManagerException the access manager exception
     */
    void detachDriver(DriverComplementInfo aDriver, PolicyVersion aPolicyVersion, ApplicationEnum application)
            throws AccessManagerException;

    /**
     * Validate hard roadblocks for QuickQuote
     *
     * @param aDriver {@link DriverComplementInfo}
     * @param locale {@link Locale}
     * @return {@link RuleExceptionResult} on error, null when valid
     */
    RuleExceptionResult validateQuickQuoteHardRoadblock(DriverComplementInfo aDriver, Locale locale);

    /**
     * Validates QuickQuote the soft roadblocks for a driver.
     *
     * @param policyVersion - {@link PolicyVersion}
     * @param aProvince
     * @return the list of soft roadblock exceptions
     */
    List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(PolicyVersion policyVersion, ProvinceCodeEnum aProvince);

    /**
     * Validates QuickQuote the soft roadblocks for a driver.
     *
     * @param driver - {@link DriverComplementInfo}
     * @param aProvince
     * @return the list of soft roadblock exceptions
     */
    List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(DriverComplementInfo driver, ProvinceCodeEnum aProvince);

    /**
     * Manage the universityDegree group
     *
     * @param party a party
     * @param insuredGroupUni a insuredGroupUni
     */
    void manageUniversityDegree(Party party, final String insuredGroupUni);

    /**
     * Gets the insured group from service.
     *
     * @param province the province
     * @param partyGroupType the party group type
     * @param partyGroupCode the party group code
     * @param context the context
     * @param distributor The Distributor code for which to get the insured group; accepts null if parameter does not apply (e.g. for Intact)
     * @return the insured group from service
     */
    String getInsuredGroupFromService(Province province, PartyGroupType partyGroupType, String partyGroupCode,
                                      ManufacturingContext context, final DistributorCodeEnum distributor);

    /**
     * Manage the profile of a quote. Added for the quickquote project.
     *
     * @param policyVersion the policy version
     * @param application the application
     * @throws SingleIdActiveProductException the single id active product exception
     * @throws AccessManagerException the access manager exception
     */
    void manageProfile(PolicyVersion policyVersion, ApplicationEnum application) throws SingleIdActiveProductException,
            AccessManagerException;

    /**
     * Gets the work sectors (job domain) list filtered by distributor
     *
     * @param locale the locale
     * @param distributionChannel {@link DistributionChannelCodeEnum}: it discriminates the distribution channel
     *            (Broker) Intact from Belair (Direct seller)
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @param distributor {@link DistributorCodeEnum}: it discriminates Belair or BNA as the distributor
     *
     * @return the domains list
     */
    List<ValidValueBO> getDomainsList(Locale locale, DistributionChannelCodeEnum distributionChannel,
                                      InsuranceBusinessCodeEnum insuranceBusiness, DistributorCodeEnum distributor);

    /**
     * returns a occupations list.
     *
     * @param locale the locale
     * @param aDomain the a domain
     * @param context the policy context
     *
     * @return the occupations list
     */
    List<ValidValueBO> getOccupationsList(Locale locale, String aDomain, ManufacturingContext context, DistributorCodeEnum distributor);
}
