/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
* without the written permission of Intact Insurance
* Copyright (c) 2017, Intact Insurance, All rights reserved.<br>
*/
package intact.lab.autoquote.backend.quotestatemanager.impl;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.common.util.localizedcontext.annotation.AutowiredLocal;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.driver.BR449_LimitDrivers;
import com.intact.business.rules.offer.BR263_1_MonthlyPremiumAvailable;
import com.intact.business.rules.vehicle.BR1797_LimitTwoVehicle;
import com.intact.com.state.ComState;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import intact.lab.autoquote.backend.quotestatemanager.IQuoteStateManager;
import org.apache.commons.lang3.BooleanUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR> L.
 *
 */
public abstract class QuoteStateManagerIntact implements IQuoteStateManager {

	private static final Logger LOGGER = ESAPI.getLogger(QuoteStateManagerIntact.class);

	@AutowiredLocal
	private IDriverBusinessProcess driverBusinessProcess;

	@AutowiredLocal
	private IVehicleBusinessProcess vehicleBusinessProcess;

	@Autowired
	protected IQuotationService quotationService;

	@Autowired
	protected BR263_1_MonthlyPremiumAvailable BR263;

	@Override
	public void updateState(ComState aComState, PolicyVersion aPolicyVersion) {
		// -- General
		aComState.setRoadblockInd(null);
		aComState.setFirstRate(null);

		// -- Driver
		aComState.setCanAddDriver(false);
		aComState.setCanRemoveDriver(false);
		aComState.setShowPreviousAddress(null);

		// -- Vehicle
		aComState.setCanAddVehicle(false);
		aComState.setCanRemoveVehicle(false);

		// -- Offer
		aComState.setCanRate(this.canRate(aComState, aPolicyVersion));
		aComState.setCanBind(false);
		aComState.setHasOffer(null);
		aComState.setMonthlyPaymentsEligible(this.isMonthlyPaymentsEligible(aPolicyVersion));

		// -- Usage
		aComState.setCanShowUsage(false);
	}

	/**
	 * Determine if rating is allowed.
	 *
	 * @return {@link Boolean} True if rating is allowed.
	 */
	private boolean canRate(ComState aComState, PolicyVersion aPolicyVersion) {
		boolean canRate = false;

		if (BooleanUtils.isNotTrue(aComState.getRoadblockInd())) {
			// Can rate when :
			// -- 1 vehicle present and does not exceed the limit of vehicles
			// -- 1 driver present and does not exceed the limit of drivers

			int vehCount = this.vehicleBusinessProcess.getVehicleCount(aPolicyVersion);
			boolean validAmountVehicles = vehCount > 0 && vehCount <= BR1797_LimitTwoVehicle.getMaxVehicle();

			int drvCount = this.driverBusinessProcess.getDriversCount(aPolicyVersion);
			boolean validAmountDrivers = drvCount > 0 && drvCount <= BR449_LimitDrivers.getMaxDrivers();

			canRate = validAmountVehicles && validAmountDrivers;
			if (LOGGER.isDebugEnabled()) {
				LOGGER.debug(Logger.EVENT_SUCCESS, new StringBuilder(">> canRate='").append(canRate).append("', vehicleCount='")
						.append(vehCount).append("', driverCount='").append(drvCount).append("'").toString());
			}
		}

		return canRate;
	}

	/**
	 * Determine if monthly payment is allowed.
	 *
	 * @return {@link Boolean}
	 */
	protected boolean isMonthlyPaymentsEligible(PolicyVersion aPolicyVersion) {
		return this.BR263.validate(aPolicyVersion);
	}
}
