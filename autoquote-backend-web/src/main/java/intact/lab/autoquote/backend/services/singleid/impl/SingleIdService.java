package intact.lab.autoquote.backend.services.singleid.impl;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.singleid.accessmanager.domain.IState;
import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;
import intact.lab.autoquote.backend.services.singleid.IProfileService;
import intact.lab.autoquote.backend.services.singleid.ISingleIdService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
public class SingleIdService implements ISingleIdService {

    private static final Logger LOG = ESAPI.getLogger(SingleIdService.class);

    private final IProfileService profileService;

    public SingleIdService(@Lazy IProfileService profileService) {
        this.profileService = profileService;
    }

    /**
     * Checks if the name insured has an active Life Insurance or Residential Policy.
     *
     * @param clientId the client id
     * @return the boolean
     * @throws AccessManagerException
     * @throws Exception the exception
     */
    @Override
    public boolean hasActiveLifeAutoOrResidentialProduct(Long clientId) throws AccessManagerException {

        boolean activeResidentialPolicy = this.hasActiveResidentialPolicy(clientId);

        boolean activeLifeInsurancePolicy = this.hasActiveLifeInsurancePolicy(clientId);

        boolean activeAutoPolicy = this.hasActiveAutoPolicy(clientId);

        return activeResidentialPolicy || activeLifeInsurancePolicy || activeAutoPolicy;
    }

    protected boolean hasActiveResidentialPolicy(Long clientId) throws AccessManagerException {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format("clientId=[%d] :: Verify residential policy state", clientId));
        }
        // check state of the residential policy
        int residentialState = this.profileService.getClientAccountService().getResidentialPolicyState(clientId);

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format("clientId=[%d] :: residentialState=[%d] (valid==%d)", clientId, residentialState, IState.RESIDENTIALPOLICY_VALID));
        }

        return (residentialState == IState.RESIDENTIALPOLICY_VALID);
    }

    protected boolean hasActiveLifeInsurancePolicy(Long clientId) throws AccessManagerException{
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format("clientId=[%d] :: Verify life insurance state", clientId));
        }
        // check the state of the life insurance policy
        int lifeInsuranceState = this.profileService.getClientAccountService().getLifeInsuranceState(clientId);

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format("clientId=[%d] :: lifeInsuranceState=[%d] (valid==%d)", clientId, lifeInsuranceState, IState.LIFEPOLICY_VALID));
        }
        return (lifeInsuranceState == IState.LIFEPOLICY_VALID);
    }

    protected boolean hasActiveAutoPolicy(Long clientId) throws AccessManagerException{
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format("clientId=[%d] :: Verify Auto insurance state", clientId));
        }
        // check the state of the auto policy
        int autoState = this.profileService.getClientAccountService().getAutoPolicyState(clientId);

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, String.format("clientId=[%d] :: autoInsuranceState=[%d] (valid=={%d,%d})", clientId, autoState,
                    IState.AUTOPOLICY_VALID, IState.AUTOPOLICY_FUTURE));
        }
        return (autoState == IState.AUTOPOLICY_VALID || autoState == IState.AUTOPOLICY_FUTURE);
    }

    /**
     * Checks if the name insured has active auto quotes.
     *
     * @param clientId the client id
     *
     * @return the boolean
     * @throws AccessManagerException
     */
    @Override
    public boolean hasActiveAutoQuotes(Long clientId) throws AccessManagerException {

        String clientInfo = "";
        if (LOG.isDebugEnabled()) {
            clientInfo = new StringBuilder("clientId=").append(clientId).append(", ").toString();
            LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder(clientInfo).append(" Verify Auto Quote insurance state").toString());
        }

        int autoQuoteState = this.profileService.getClientAccountService().getAutoQuoteState(clientId, ApplicationModeEnum.REGULAR_QUOTE);

        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder(clientInfo).append("AutoQuoteState = ").append(autoQuoteState).toString());
        }

        if (autoQuoteState == IState.AUTOQUOTE_UNCOMPLETE  || autoQuoteState == IState.AUTOQUOTE_BOUND) {
            return true;
        }

        return false;

    }
}
