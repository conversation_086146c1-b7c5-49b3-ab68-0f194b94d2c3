/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.singleid;

import com.ing.canada.singleid.accessmanager.exception.AccessManagerException;

/**
 * <AUTHOR>
 * 
 */
public interface ISingleIdService {

	/**
	 * Checks if the name insured has an active Life Insurance or Residential Policy.
	 * 
	 * @param clientId the client id
	 * @return the boolean
	 * @throws AccessManagerException
	 */
	boolean hasActiveLifeAutoOrResidentialProduct(Long clientId) throws AccessManagerException;


	/**
	 * Checks if the name insured has an active auto quotes
	 * 
	 * @param clientId the client id
	 * @return the boolean
	 * @throws AccessManagerException
	 */
	boolean hasActiveAutoQuotes(Long clientId) throws AccessManagerException;

}
