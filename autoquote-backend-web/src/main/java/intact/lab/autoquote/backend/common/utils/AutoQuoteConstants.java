package intact.lab.autoquote.backend.common.utils;


/**
 * The Class QuickQuoteConstants.
 */
public class AutoQuoteConstants {

	/**
	 * Quebec province short form
	 */
	public static final String STR_QC = "QC";
	/**
	 * Ontario province short form
	 */
	public static final String STR_ON = "ON";
	/**
	 * Alberta province short form
	 */
	public static final String STR_AB = "AB";
	/**
	 * French language
	 */
	public static final String STR_FR = "FR";
	/**
	 * English language
	 */
	public static final String STR_EN = "EN";
	/**
	 * Company name
	 */
	public static final String STR_INTACT = "INTACT";
	/**
	 * The Constant STR_BLANK.
	 */
	public static final String STR_BLANK = "";
	/**
	 * The Constant STR_NULL.
	 */
	public static final String STR_NULL = "null";
	/**
	 * The Constant STR_INT_0.
	 */
	public static final String STR_INT_0 = "0";
	/**
	 * The Constant STR_INT_1.
	 */
	public static final String STR_INT_1 = "1";
	/**
	 * The Constant STR_INT_1.
	 */
	public static final String STR_INT_2 = "2";
	/**
	 * The Constant STR_INT_5.
	 */
	public static final String STR_INT_5 = "5";
	/**
	 * The Constant STR_TRUE.
	 */
	public static final String STR_TRUE = "true";
	/**
	 * The Constant STR_FALSE.
	 */
	public static final String STR_FALSE = "false";
	/**
	 * The Constant STR_NONE.
	 */
	public static final String STR_NONE = "none";
	/**
	 * The Constant STR_YES.
	 */
	public static final String STR_YES = "yes";
	/**
	 * The Constant MONTANT_PREMIUM_MIN_QC.
	 */
	public static final int MONTANT_PREMIUM_MIN_QC = 100;
	/**
	 * The Constant MONTANT_PREMIUM_MIN_ON.
	 */
	public static final int MONTANT_PREMIUM_MIN_ON = 100;
	/**
	 * The Constant MONTANT_PREMIUM_MIN_AB.
	 */
	public static final int MONTANT_PREMIUM_MIN_AB = 100;
	/**
	 * The Constant ROADBLOCK.
	 */
	public static final String ROADBLOCK = "ROADBLOCK";
	/**
	 * The Constant SUCCESS.
	 */
	public static final String SUCCESS = "SUCCESS";

	/**
	 * Instantiates a new quick quote constants.
	 */
	private AutoQuoteConstants() {

	}

}
