package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.enums.ContentsMappingEnum;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.common.utils.AutoQuoteConstants;
import intact.lab.autoquote.backend.services.impl.AutoQuoteServiceCache;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import java.util.List;

@Component
public class BusinessKmValidationRule {

	private final AutoQuoteServiceCache autoQuoteServiceCache;

	public BusinessKmValidationRule(AutoQuoteServiceCache autoQuoteServiceCache) {
		this.autoQuoteServiceCache = autoQuoteServiceCache;
	}

	public void validate(String businessKm, String province, String language, Errors errors) {
		if (businessKm == null) { // businessKm is not required, so null is valid
			return;
		}
		final List<ValidValueBO> businessKmList = autoQuoteServiceCache.getListByProvinceAndLocaleBO(ContentsMappingEnum.BUSINESS_KM.name(), province, language);
		if ((!businessKm.equals(AutoQuoteConstants.STR_INT_0) && !ValidationUtilities.isExistValueInList(businessKmList, businessKm))) {
			errors.rejectValue("businessKm", BRulesExceptionEnum.RoadBlock.getErrorCode(), "[businessKm]");
		}
	}

}
