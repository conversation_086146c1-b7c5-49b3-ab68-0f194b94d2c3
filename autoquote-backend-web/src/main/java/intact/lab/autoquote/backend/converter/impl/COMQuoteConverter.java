package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.CommunicationObjectModel;
import com.intact.com.address.ComAddress;
import com.intact.com.address.ComMunicipalityInfo;
import com.intact.com.ajax.ValidValue;
import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.driver.ComDriver;
import com.intact.com.enums.ComBrokerWebSiteOriginEnum;
import com.intact.com.enums.ComPolicyDiscountCodeEnum;
import com.intact.com.util.ComDate;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.PolicyHolderDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.dto.RiskDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.enums.WebSiteOriginEnum;
import intact.lab.autoquote.backend.common.model.ValidValueBO;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.converter.IMunicipalityHelper;
import intact.lab.autoquote.backend.services.IValueDomainService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

@Component("comQuoteConverter")
public class COMQuoteConverter implements ICOMConverter<QuoteDTO, CommunicationObjectModel> {

	protected IValueDomainService valueDomainService;

	@Resource(name = "comPolicyHolderConverter")
	private ICOMConverter<PolicyHolderDTO, ComDriver> comPolicyHolderConverter;

	@Resource(name = "comPartyConverter")
	private ICOMConverter<PartyDTO, ComDriver> comPartyConverter;

	@Resource(name = "comVehicleConverter")
	private ICOMConverter<VehicleDTO, ComVehicle> comVehicleConverter;

	@Resource(name = "comPartyRoleConverter")
	private COMPartyRoleConverter comPartyRoleConverter;

	@Resource(name = "comDriverConverter")
	private ICOMConverter<DriverDTO, ComDriver> comDriverConverter;

	@Resource(name = "comDistributorConverter")
	private ICOMConverter<DistributorDTO, ComBrokerInfo> comDistributorConverter;

	@Resource(name = "municipalityHelper")
	private IMunicipalityHelper municipalityHelper;

	public COMQuoteConverter(IValueDomainService valueDomainService) {
		this.valueDomainService = valueDomainService;
	}

	@Override
	public QuoteDTO toDTO(CommunicationObjectModel com) {
		QuoteDTO quote = new QuoteDTO();
		if (com != null) {
			quote.setId(com.getUuId());
			quote.setNumber(com.getAgreementNumber());
			quote.setPvId(com.getPolicyVersionId().toString());
			if (com.getPolicyDiscountCode() != null) {
				quote.setPolicyDiscountCode(com.getPolicyDiscountCode().getCode()); //SV
			}

			if (com.getContext() != null && com.getContext().getBrokerInfo() != null) {
				DistributorDTO distributorDTO = comDistributorConverter.toDTO(com.getContext().getBrokerInfo());
				quote.setSubBrokerNbr(distributorDTO.getNumber());
			}

			if (com.getContext().getClientXForwardIPNbr() != null) {
				quote.setClientXForwardIPNbr(com.getContext().getClientXForwardIPNbr());
			}

			loadParties(quote, com);
			loadVehicles(quote, com);
			loadDrivers(quote, com);
			if (CollectionUtils.isNotEmpty(com.getVehicles())) {
				comPartyRoleConverter.toDTO(com, quote);
			}
		}

		return quote;
	}

	@Override
	public CommunicationObjectModel toCOM(QuoteDTO quoteDTO, CommunicationObjectModel initialCOM) {
		COMInitializer comInitializer = new COMInitializer(initialCOM);
		CommunicationObjectModel com = comInitializer.initialize();
		CommunicationObjectModel baseCommunicationObjectModel = comInitializer.getBaseCommunicationObjectModel();


		if (CollectionUtils.isEmpty(com.getRoadblock()) && baseCommunicationObjectModel != null) {
			com.getRoadblock().addAll(comInitializer.getBaseCommunicationObjectModel().getRoadblock());
		}


		if (initialCOM != null && initialCOM.getState().getHasOffer() != null && initialCOM.getState().getHasOffer()) {
			com.getState().setFirstRate(false);
			com.getState().setDataChanged(Boolean.TRUE);
		}

		/*
		 * DEFECT : 193423 Re-rating after roadblock  the technique error displayed
		 * NOTE : the cause of the problem is the roadblock generate before call of the smart value,
		 * so we get technical errors pega, because of the values that are null in the new policyVersion
		 * SOLUTION : Clone policyVersion if the quote already contains an offer.
		 * TODO : the ideal solution is to change the order of the calls (smart value -> validationRoadblock-> ...), in the sequance of backup, on the other hand it requires a test of confirmation CL and
		 */
		if (StringUtils.isNotBlank(quoteDTO.getId()) && CollectionUtils.isNotEmpty(quoteDTO.getRisks()) && CollectionUtils.isNotEmpty(quoteDTO.getRisks().getFirst().getOffers())) {
			com.getState().setHasOffer(Boolean.TRUE);
		}

		if (com.getPolicyEffectiveDate() == null) {
			com.setPolicyEffectiveDate(this.convertToComDate(new DateTime()));
		}

		if (quoteDTO != null) {
			if (com.getContext() != null) {
				com.getContext().setClientXForwardIPNbr(quoteDTO.getClientXForwardIPNbr());
				com.getContext().setMarketingPromotionCode(quoteDTO.getContextDTO().getOrganizationSource());
			}

			com.setPolicyDiscountCode(StringUtils.isNotEmpty(quoteDTO.getPolicyDiscountCode()) ? ComPolicyDiscountCodeEnum.valueOf(quoteDTO.getPolicyDiscountCode()) : null);
			//boolean used by rating, to indicate that we would like to load all coverages.
			com.getContext().setUseAllCoveragesStructure(Boolean.TRUE);
			prepareParties(quoteDTO, com);
			setPolicyHolderInd(quoteDTO.getPolicyHolders(), com.getDrivers());
			preparePolicyHolders(quoteDTO, com);
			prepareDrivers(quoteDTO, com);
			prepareRisks(quoteDTO, com);
			prepareBrokerInfo(quoteDTO, com);
		}

		try {
			manageAddress(com, quoteDTO);
		} catch (Exception e) {
			//throw new ConversionException(ConversionException.EXEC_ADDRESS_CONVERSION, e);
		}
		comPartyRoleConverter.toCOM(quoteDTO, com);
		return com;
	}

	/**
	 * @param quoteDTO
	 * @param com
	 */
	private void prepareParties(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (PartyDTO party : quoteDTO.getParties()) {
			ComDriver driver = null;
			driver = matchParty(party.getId(), com.getDrivers());
			if (driver == null) {
				driver = new ComDriver();
			}
			driver = this.comPartyConverter.toCOM(party, driver);
			if (driver.getDriverId() == 0) { // at this point, id is ok
				com.getDrivers().add(driver);
			}
		}
	}


	/**
	 * @param quoteDTO
	 * @param com      Set the BrokerSearchInfo
	 *                 TODO : Initially, the conversion of the broker information must be done in the associated converter,!
	 */
	private void prepareBrokerInfo(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		ComBrokerInfo comBrokerInfo = new ComBrokerInfo();
		String subBroker = quoteDTO.getContextDTO().getSubBroker();
		comBrokerInfo.setSubBrokerNumber(quoteDTO.getSubBrokerNbr() != null ? quoteDTO.getContextDTO().getSubBroker() : subBroker);
		comBrokerInfo.setBrokerWebsiteOrigin(WebSiteOriginEnum.WEBBK.equals(quoteDTO.getContextDTO().getOrigin()) ? ComBrokerWebSiteOriginEnum.BROKER : ComBrokerWebSiteOriginEnum.INTACT);
		if (com.getDrivers() != null) {
			for (ComDriver driver : com.getDrivers()) {
				if (driver.getPolicyHolderInd()) {
					PartyDTO partyDTO = this.findParty(quoteDTO.getParties(), driver.getWebMsgId());
					AddressDTO addressDTO = partyDTO.getAddress();
					if (addressDTO != null) {
						comBrokerInfo.setPostalCode(addressDTO.getPostalCode());
					}
				}
			}
		}
		com.getContext().setBrokerInfo(comBrokerInfo);
	}

	private void manageAddress(CommunicationObjectModel com, QuoteDTO quoteDTO) throws Exception {
		Locale locale = quoteDTO.getContextDTO().getLocale();
		for (ComDriver driver : com.getDrivers()) {
			if (driver.getPolicyHolderInd()) {
				PartyDTO partyDTO = this.findParty(quoteDTO.getParties(), driver.getWebMsgId());
				AddressDTO addressDTO = partyDTO.getAddress();
				if (addressDTO.getPostalCode() == null) continue;
				ComMunicipalityInfo comMunInfo = municipalityHelper.getMunicipalityInfo(com, addressDTO);
				ComAddress address = com.getDrivers().getFirst().getCurrentAddress();
				address.setCountry(comMunInfo.getCountry());
				address.setProvince(addressDTO.getProvince());
				// story 66477 in ON we take the first as default - no ui to choose.
				if (CollectionUtils.isNotEmpty(comMunInfo.getMunicipalities())) {
					address.setCity(comMunInfo.getMunicipalities().getFirst().getLabel());
					for (ValidValue municipalitie : comMunInfo.getMunicipalities()) {
						if (addressDTO.getMunicipalityCode().equals(municipalitie.getValue())) {
							address.setCityCode(municipalitie.getValue());
							address.setCity(municipalitie.getLabel());
							break;
						}
					}
				}

				StringBuilder streetName = new StringBuilder();
				if (locale.getLanguage().equals(Locale.ENGLISH.getLanguage())) {
					// English
					if (StringUtils.isNotBlank(comMunInfo.getStreetName())) {
						streetName.append(comMunInfo.getStreetName());
					}
					if (StringUtils.isNotBlank(comMunInfo.getStreetType())) {
						streetName.append(" ").append(ValidValueBO.getLabelForValue(this.valueDomainService.getListStreetType(locale), comMunInfo.getStreetType()));
					}
				} else {
					// French
					if (StringUtils.isNotBlank(comMunInfo.getStreetType())) {
						streetName.append(ValidValueBO.getLabelForValue(this.valueDomainService.getListStreetType(locale), comMunInfo.getStreetType())).append(" ");
					}
					if (StringUtils.isNotBlank(comMunInfo.getStreetName())) {
						streetName.append(comMunInfo.getStreetName());
					}
				}

				if (StringUtils.isNotBlank(comMunInfo.getStreetDirection())) {
					streetName.append(" ").append(comMunInfo.getStreetDirection());
				}

				address.setStreetName(streetName.toString());
			}
		}
	}

	private void preparePolicyHolders(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (PolicyHolderDTO policyHolder : quoteDTO.getPolicyHolders()) {
			// for each policyHolder, add info to initial driver (no new Driver must be created)
			ComDriver comDriver = null;
			comDriver = matchPolicyHolder(com.getDrivers());
			if (comDriver != null) {
				this.comPolicyHolderConverter.toCOM(policyHolder, comDriver);
			}

		}
	}

	private void prepareDrivers(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (DriverDTO driver : quoteDTO.getDrivers()) {
			ComDriver comDriver = matchParty(driver.getPartyId(), com.getDrivers());
			if (comDriver != null) {
				this.comDriverConverter.toCOM(driver, comDriver);
			}
		}
	}

	private void prepareRisks(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (RiskDTO risk : quoteDTO.getRisks()) {
			if (risk instanceof VehicleDTO) {
				boolean addVehicle = false;
				ComVehicle comVehicle = matchVehicle(risk.getId(), com.getVehicles());
				if (comVehicle == null) {
					addVehicle = true;
				}
				comVehicle = this.comVehicleConverter.toCOM((VehicleDTO) risk, comVehicle);
				if (addVehicle) {
					com.getVehicles().add(comVehicle);
				}
			}
		}
	}

	private ComDriver matchPolicyHolder(List<ComDriver> drivers) {
		for (ComDriver comDriver : drivers) {
			if (comDriver == null) return null;
			if (comDriver.getPolicyHolderInd() != null && comDriver.getPolicyHolderInd()) {
				return comDriver;
			}
		}
		return null;
	}

	private void setPolicyHolderInd(List<PolicyHolderDTO> policyHolers, List<ComDriver> drivers) {
		for (ComDriver comDriver : drivers) {
			for (PolicyHolderDTO policyHolder : policyHolers) {
                comDriver.setPolicyHolderInd(Objects.equals(comDriver.getWebMsgId(), policyHolder.getPartyId()));
			}
		}
	}

	private PartyDTO findParty(List<PartyDTO> parties, int id) {
		for (PartyDTO partyDTO : parties) {
			if (partyDTO.getId() == id) {
				return partyDTO;
			}
		}
		return null;
	}

	private ComDriver matchParty(int id, List<ComDriver> drivers) {
		for (ComDriver comDriver : drivers) {
			if (comDriver == null) return null;
			if (id == comDriver.getWebMsgId()) {
				return comDriver;
			}
		}
		return null;
	}

	private ComVehicle matchVehicle(int id, List<ComVehicle> vehicles) {
		for (ComVehicle comVehicle : vehicles) {
			if (id == comVehicle.getWebMsgId()) {
				return comVehicle;
			}
		}
		return null;
	}


	/**
	 * @param quoteDTO
	 * @param com
	 */
	private void loadParties(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (ComDriver comDriver : com.getDrivers()) {
			PartyDTO party = this.comPartyConverter.toDTO(comDriver);
			if (comDriver.getPolicyHolderInd()) {
				PolicyHolderDTO policyHolder = this.comPolicyHolderConverter.toDTO(comDriver);
				policyHolder.setPartyId(comDriver.getWebMsgId());
				party.setId(comDriver.getWebMsgId());
				quoteDTO.getPolicyHolders().add(policyHolder);
			}
			quoteDTO.getParties().add(party);
		}
	}

	/**
	 * @param quoteDTO
	 * @param com
	 */
	private void loadDrivers(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (ComDriver comDriver : com.getDrivers()) {
			if (comDriver.getIsDriver() != null && comDriver.getIsDriver()) {
				DriverDTO driver = this.comDriverConverter.toDTO(comDriver);
				quoteDTO.getDrivers().add(driver);
			}
		}
	}


	/**
	 * @param quoteDTO
	 * @param com
	 */
	private void loadVehicles(QuoteDTO quoteDTO, CommunicationObjectModel com) {
		for (ComVehicle comVehicle : com.getVehicles()) {
			VehicleDTO vehicle = this.comVehicleConverter.toDTO(comVehicle);
			quoteDTO.getRisks().add(vehicle);
		}
	}

//	public ICOMConverter<PolicyHolderDTO, ComDriver> getComPolicyHolderConverter() {
//		return comPolicyHolderConverter;
//	}
//
//	public void setComPolicyHolderConverter(ICOMConverter<PolicyHolderDTO, ComDriver> comPolicyHolderConverter) {
//		this.comPolicyHolderConverter = comPolicyHolderConverter;
//	}
//
//	public ICOMConverter<PartyDTO, ComDriver> getComPartyConverter() {
//		return comPartyConverter;
//	}
//
//	public void setComPartyConverter(ICOMConverter<PartyDTO, ComDriver> comPartyConverter) {
//		this.comPartyConverter = comPartyConverter;
//	}
//
//	/**
//	 * @return the comVehicleConverter
//	 */
//	public ICOMConverter<VehicleDTO, ComVehicle> getComVehicleConverter() {
//		return comVehicleConverter;
//	}
//
//	/**
//	 * @param comVehicleConverter the comVehicleConverter to set
//	 */
//	public void setComVehicleConverter(
//			ICOMConverter<VehicleDTO, ComVehicle> comVehicleConverter) {
//		this.comVehicleConverter = comVehicleConverter;
//	}
//
//	public COMPartyRoleConverter getComPartyRoleConverter() {
//		return comPartyRoleConverter;
//	}
//
//	public void setComPartyRoleConverter(COMPartyRoleConverter comPartyRoleConverter) {
//		this.comPartyRoleConverter = comPartyRoleConverter;
//	}
//
//	public ICOMConverter<DriverDTO, ComDriver> getComDriverConverter() {
//		return comDriverConverter;
//	}
//
//	public void setComDriverConverter(
//			ICOMConverter<DriverDTO, ComDriver> comDriverConverter) {
//		this.comDriverConverter = comDriverConverter;
//	}
//
//	public IValueDomainService getValueDomainService() {
//		return valueDomainService;
//	}
//
//	public void setValueDomainService(IValueDomainService valueDomainService) {
//		this.valueDomainService = valueDomainService;
//	}

	/**
	 * Convert to com date.
	 *
	 * @param dateTime the date time
	 * @return the com date
	 */
	public ComDate convertToComDate(DateTime dateTime) {
		Assert.notNull(dateTime, "DateTime parameter must be initialized.");
		ComDate comDate = new ComDate();
		comDate.setYear(Integer.toString(dateTime.getYear()));
		comDate.setMonth(Integer.toString(dateTime.getMonthOfYear()));
		comDate.setDay(Integer.toString(dateTime.getDayOfMonth()));
		return comDate;
	}

}
