package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

@Component
public class QuoteDTOValidatorAB extends AbstractQuoteDTOValidator {

	protected void validateDriver(DriverDTO driver, Errors errors, ValidationContext context) {
		super.validateDriver(driver, errors, context);
		this.driverDTOValidator.validateLicenseObtentionDate(driver.getLicenseObtentionDate(), errors, context);
	}

}
