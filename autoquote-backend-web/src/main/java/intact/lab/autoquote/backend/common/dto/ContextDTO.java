package intact.lab.autoquote.backend.common.dto;

import intact.lab.autoquote.backend.common.enums.LanguageEnum;
import intact.lab.autoquote.backend.common.enums.WebSiteOriginEnum;
import intact.lab.autoquote.backend.common.model.BusinessContext;
import lombok.Data;

import java.util.Locale;

@Data
public class ContextDTO {

    private LanguageEnum language = null;
    private Locale locale = null;
    private BusinessContext businessContext;
    private String ipAddress = null;
    private String subBroker = null;
    private String organizationSource = null;
    private WebSiteOriginEnum origin = null;
    private String clientXForwardIPNbr;
}
