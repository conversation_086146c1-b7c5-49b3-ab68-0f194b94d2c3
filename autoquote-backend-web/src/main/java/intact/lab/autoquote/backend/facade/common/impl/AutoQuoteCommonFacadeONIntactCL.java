package intact.lab.autoquote.backend.facade.common.impl;

import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.helper.impl.VehicleHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.context.ComContext;
import com.intact.com.vehicle.ComVehicle;
import intact.lab.autoquote.backend.common.exception.AutoQuoteQuoteServiceException;
import intact.lab.autoquote.backend.common.exception.AutoquoteFacadeException;
import intact.lab.autoquote.backend.config.RatingConfig;
import intact.lab.autoquote.backend.converter.impl.COMQuoteConverter;
import intact.lab.autoquote.backend.facade.impl.AutoQuoteCommonFacade;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import intact.lab.autoquote.backend.services.impl.BloomMQHandlerService;
import intact.lab.autoquote.backend.services.impl.BrokerService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.Date;


@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class AutoQuoteCommonFacadeONIntactCL extends AutoQuoteCommonFacade {


	AutoQuoteCommonFacadeONIntactCL(@Qualifier("cifSubBrokersService") ISubBrokersService subBrokersService, ICommonBusinessProcess commonBusinessProcess,
									RatingConfig ratingConfig, COMQuoteConverter comQuoteConverter, IInsurancePolicyService insurancePolicyService,
									VehicleHelper vehicleHelper, BloomMQHandlerService bloomMQHandlerService, BrokerService brokerService) {
		super(subBrokersService, commonBusinessProcess, comQuoteConverter, ratingConfig, insurancePolicyService,
				vehicleHelper, bloomMQHandlerService, brokerService);
	}

	@Override
	public CommunicationObjectModel save(CommunicationObjectModel aCom, boolean force) throws AutoQuoteQuoteServiceException,
			AutoquoteFacadeException {
		this.managePolicyVersionEffectiveDate(aCom);
		return super.save(aCom, force);
	}
	/**
	 * Gets the overriding rating date from autoquote-rating.properties and set it to policyEffectiveDate.
	 * Since the winter tire discount will not be available before
	 * January, 1st 2016, we set policyEffectiveDate to a future date so QA team can test it now.
	 * This method will not be useful after that date.
	 * */
	private void managePolicyVersionEffectiveDate(CommunicationObjectModel aCom) {
		ComContext context = aCom.getContext();
		Date overridingRatingDate = this.ratingConfig.getOverridingRatingDate(context.getProvince().getCode());
		if (overridingRatingDate != null) {
			aCom.setPolicyEffectiveDate(this.comQuoteConverter.convertToComDate(new DateTime(overridingRatingDate.getTime())));
		}
	}

	@Override
	protected void ajustPrincipalDriverAndRegisterOwner(
			CommunicationObjectModel quote, ComVehicle vehicle) {
		// no implementation required here
	}

	@Override
	public CommunicationObjectModel callCreditScore(CommunicationObjectModel com) throws AutoquoteFacadeException {
		return com;
	}

}
