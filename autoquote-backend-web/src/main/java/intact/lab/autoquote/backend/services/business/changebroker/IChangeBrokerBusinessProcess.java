package intact.lab.autoquote.backend.services.business.changebroker;

import com.ing.canada.plp.domain.enums.AssignmentOriginatorTypeCodeEnum;
import com.ing.canada.plp.domain.enums.AssignmentReasonCodeEnum;

public interface IChangeBrokerBusinessProcess {

    /**
     * Change the client sub Broker.
     *
     * @param policyVersion
     * @param subBrokerNumber
     * @param companyNumber
     * @param reason
     */
    void changeClientBroker(Long policyVersion, String subBrokerNumber, String companyNumber,
                            AssignmentReasonCodeEnum reason);

    /**
     * Change the client sub Broker.
     *
     * @param policyVersion
     * @param subBrokerNumber
     * @param companyNumber
     * @param reason
     */
    void changeClientBroker(Long policyVersion, String subBrokerNumber, String companyNumber,
                            AssignmentReasonCodeEnum reason, AssignmentOriginatorTypeCodeEnum assignmentOriginator);
}
