package intact.lab.autoquote.backend.services.business.vehicle;

import com.ing.canada.common.domain.VehicleModel;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.intact.business.rules.exception.RuleExceptionResult;

import java.util.List;
import java.util.Locale;

public interface IVehicleBusinessProcess {

    /**
     * Gets the number of vehicle contained in a PolicyVersion.
     *
     * @param aPolicyVersion - {@link PolicyVersion}
     * @return the Vehicle count
     */
    int getVehicleCount(PolicyVersion aPolicyVersion);


    /**
     * Gets the vehicle manufacturer list by year.
     *
     * @param year the year
     * @param distributionchannel {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @param provinceCodeEnum {@link ProvinceCodeEnum}
     *
     * @return the vehicle manufacturer list by year
     */
    List<String> getVehicleManufacturerListByYear(String year, DistributionChannelCodeEnum distributionchannel,
                                                  InsuranceBusinessCodeEnum insuranceBusiness, ProvinceCodeEnum provinceCodeEnum);

    /**
     * Gets the vehicle model list.
     *
     * @param language the language
     * @param make the make
     * @param year the year
     * @param distributionChannel {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @param province {@link ProvinceCodeEnum}
     *
     * @return the vehicle model list
     */
    List<VehicleModel> getVehicleModelList(Language language, String make, String year,
                                           DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness,
                                           ProvinceCodeEnum province);

    /**
     * Get a vehicle model in english for a given modelCode, make and year.
     *
     * @param modelCode the model code
     * @param make the make
     * @param year the year
     * @param distributionChannel {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @param province {@link ProvinceCodeEnum}
     *
     * @return the vehicle model english by model code
     *
     */
    VehicleModel getVehicleModelEnglishByModelCode(String modelCode, String make, String year,
                                                   DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness,
                                                   ProvinceCodeEnum province);

    /**
     * Get a vehicle model in french for a given modelCode, make and year.
     *
     * @param modelCode the model code
     * @param make the make
     * @param year the year
     * @param distributionChannel {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness {@link InsuranceBusinessCodeEnum}
     * @param province {@link ProvinceCodeEnum}
     *
     * @return the vehicle model french by model code
     *
     */
    VehicleModel getVehicleModelFrenchByModelCode(String modelCode, String make, String year,
                                                  DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness,
                                                  ProvinceCodeEnum province);

    /**
     * Remove a vehicle from policy version and remove the policy version's relationships. <br>
     *
     * => This version of the method does not directly persist. <= <br>
     * It only removes the relationships from a vehicle to its' linked policyVersion
     *
     * @param aVehicle - {@link Vehicle}
     * @param aPolicyVersion - {@link PolicyVersion}
     * @param application - {@link ApplicationEnum}
     * @throws Exception
     */
    void detachVehicle(Vehicle aVehicle, PolicyVersion aPolicyVersion, ApplicationEnum application) throws Exception;

    /**
     * gets the information about the vehicle (rate group etc) and then sets it in pl.<br>
     *
     * ==> Can be overridden at class level (see intact on) <==
     *
     * @param aVehicle the plp {@link Vehicle} to set
     * @param locale the {@link Locale}
     * @param aPolicyVersionId the policy version id as {@link Long}
     * @param distributionChannel as {@link DistributionChannelCodeEnum}
     * @param insuranceBusiness as {@link InsuranceBusinessCodeEnum}
     */
    void setVehicleDetail(Vehicle aVehicle, Locale locale, Long aPolicyVersionId,
                          DistributionChannelCodeEnum distributionChannel, InsuranceBusinessCodeEnum insuranceBusiness);

    /**
     * Validate AutoQuote hard road block.
     *
     * @param aVehicle {@link Vehicle}
     * @param aProvince {@link Province}
     * @param aLanguage {@link Language}
     */
    RuleExceptionResult validateHardRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage);

    /**
     * Validate QuickQuote soft road block.
     *
     * @param aVehicle {@link Vehicle}
     * @param aProvince {@link Province}
     * @param aLanguage {@link Language}
     */
    List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage);

}
