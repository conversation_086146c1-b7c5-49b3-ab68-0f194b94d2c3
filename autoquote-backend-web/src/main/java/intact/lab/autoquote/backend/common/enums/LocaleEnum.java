package intact.lab.autoquote.backend.common.enums;

import org.apache.commons.lang3.StringUtils;

/***/
public enum LocaleEnum {

	QC_FR("QC_FR", "fr_CA_QC"),
	QC_EN("QC_EN", "en_CA_QC"),
	ON_FR("ON_FR", "fr_CA_ON"),
	ON_EN("ON_EN", "en_CA_ON"),
	AB_FR("AB_FR", "fr_CA_AB"),
	AB_EN("AB_EN", "en_CA_AB");

	private String localeCode;

	private String localeLabel;

	/***/
	LocaleEnum(String localeCode, String localeLabel) {
		this.localeCode = localeCode;
		this.localeLabel = localeLabel;
	}

	/**
	 * Value of code.
	 *
	 * @param code the code
	 * @return the additional interest type code enum
	 */
	public static LocaleEnum valueOfCode(String code) {

		if (StringUtils.isEmpty(code)) {
			return null;
		}

		for (LocaleEnum v : values()) {
			if (v.localeCode.equals(code)) {
				return v;
			}
		}
		throw new IllegalArgumentException("no value found for code: " + code);
	}

	public String getLocaleCode() {
		return localeCode;
	}

	public String getLocaleLabel() {
		return localeLabel;
	}
}
