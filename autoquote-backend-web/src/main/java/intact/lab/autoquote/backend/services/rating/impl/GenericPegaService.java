/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.rating.impl;

import com.ing.canada.common.services.impl.pegaservices.PegaBaseService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.sombase.ModelFactory;
import com.ing.canada.ss.base.BaseException;
import com.ing.canada.ss.base.SSDC;
import com.ing.canada.ss.delegate.services.GenericDelegate;
import com.ing.canada.ss.stub.pega.RegionLocator;
import intact.lab.autoquote.backend.services.rating.IGenericPegaService;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class GenericPegaService extends PegaBaseService implements IGenericPegaService {

	/** log */
	private static final Logger log = ESAPI.getLogger(GenericPegaService.class);

	/** Service properties */
	private String componentName;

	private String serviceName;

	private String serviceVersion;

	@Override
	public Object executeService(ManufacturingContext aCtxt, Object object) throws BaseException {

		Map<String, Object> pegaParams = this.getDelegateParameters(aCtxt);

		return this.executeService (pegaParams, object);

	}

	@Override
	public Object executeService(Map<String, Object> pegaParams, Object object) throws BaseException {
		// call the service
		pegaParams.put(SSDC.APPLICATION_ID, this.getApplicationId(object));

		String regionalizedServiceName = RegionLocator.getRatingServiceNameBasedOnRegion(this.serviceName, (String) pegaParams.get(SSDC.DISTRIBUTION_CHANNEL), (String) pegaParams.get(SSDC.PROVINCE), (String) pegaParams.get(SSDC.INSURANCE_BUSINESS));

		// Set the root object for proper service logging
		ModelFactory.getInstance().getGenericRootObjectForModelObject(object);

		GenericDelegate service = new GenericDelegate(this.componentName, regionalizedServiceName, this.serviceVersion, object, pegaParams);

		Object output = service.executeService();

		this.resumeLogging(output);

		log.info(Logger.EVENT_SUCCESS, String.format(this.componentName + "." + regionalizedServiceName + "." + this.serviceVersion + "> TraceId=" + super.getTraceId(pegaParams)));

		return output;
	}

	/**
	 * 	Get the application identification from the som object. If not found, we use the default
	 * 	from the properties.
	 *
	 * @param object the som model
	 * @return the application id
	 */
	private String getApplicationId(Object object) {
		String applicationId = this.getApplicationId();

		if (object instanceof PolicyVersion) {
		PolicyVersion somPolicyVersion = (PolicyVersion) object;
			if (somPolicyVersion.getTheEnvironmentContext() != null && somPolicyVersion.getTheEnvironmentContext().getApplicationIdentification() != null) {
					applicationId = somPolicyVersion.getTheEnvironmentContext().getApplicationIdentification();
			}
		}
		return applicationId;
	}

	public void setComponentName(String aComponentName) {
		this.componentName = aComponentName;
	}

	public void setServiceName(String aServiceName) {
		this.serviceName = aServiceName;
	}

	public void setServiceVersion(String aServiceVersion) {
		this.serviceVersion = aServiceVersion;
	}

}
