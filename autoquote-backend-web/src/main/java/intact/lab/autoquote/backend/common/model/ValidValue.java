package intact.lab.autoquote.backend.common.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

@Data
@NoArgsConstructor
public class ValidValue implements Comparable<ValidValue> {
    private String value;
    private String label;
    private int index;

    public ValidValue(String value, String label) {
        this.value = value;
        this.label = label;
    }

    @Override
    public int compareTo(ValidValue aValidValue) {
        return Integer.compare(this.index, aValidValue.index);
    }

    public static String getLabelForValue(Collection<ValidValue> validValues, String value) {
        return validValues.stream()
                .filter(validValue -> validValue.getValue().equals(value))
                .map(ValidValue::getLabel)
                .findFirst()
                .orElse(null);
    }
}
