/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package intact.lab.autoquote.backend.services.business.vehicle.impl;

import com.ing.canada.common.domain.VehicleDetail;
import com.ing.canada.common.services.api.Language;
import com.ing.canada.common.services.api.Province;
import com.ing.canada.common.services.api.policydate.DateHelperEnum;
import com.ing.canada.common.services.api.policydate.IDateManagerService;
import com.ing.canada.common.services.api.vehicle.IVehicleDetailService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleMakesByYearService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleModelsService;
import com.ing.canada.common.util.localizedcontext.ApplicationEnum;
import com.ing.canada.common.util.localizedcontext.annotation.ComponentLocal;
import com.ing.canada.common.webmethods.api.rategroup.RetrieveVehicleRateGroupService;
import com.ing.canada.plp.domain.enums.DistributionChannelCodeEnum;
import com.ing.canada.plp.domain.enums.InsuranceBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.service.IAdditionalInterestRoleService;
import com.ing.canada.plp.service.IInsuranceRiskService;
import com.ing.canada.plp.service.IPartyRoleInRiskService;
import com.ing.canada.plp.service.IPartyService;
import com.intact.business.rules.exception.RuleExceptionResult;
import com.intact.business.rules.vehicle.BR0616_BusinessAnnualKmVsAnnualKm;
import com.intact.business.rules.vehicle.BR15484_HighRadiusKmLimit;
import com.intact.business.rules.vehicle.BR15566_MidHaulDaysPerMonthLimit;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * Business process specific to Intact Ontario . Contains only the code specific to this company#province. The common
 * code will be in the super class.
 *
 * <AUTHOR>
 */
@ComponentLocal(province = ProvinceCodeEnum.ONTARIO, application = ApplicationEnum.INTACT, lineOfBusiness = LineOfBusinessCodeEnum.COMMERCIAL_LINES)
public class VehicleBusinessProcessONIntactCL extends VehicleBusinessProcess {

	RetrieveVehicleRateGroupService retrieveVehicleRateGroupService;
	protected BR0616_BusinessAnnualKmVsAnnualKm br0616;
	BR15484_HighRadiusKmLimit br15484;
	BR15566_MidHaulDaysPerMonthLimit br15566;

	public VehicleBusinessProcessONIntactCL(VehicleMakesByYearService vehicleMakesByYearService, VehicleModelsService vehicleModelsService,
											IDateManagerService dateManagerService, IVehicleDetailService vehicleDetailService,
											RetrieveVehicleRateGroupService retrieveVehicleRateGroupService, BR0616_BusinessAnnualKmVsAnnualKm br0616,
											BR15484_HighRadiusKmLimit br15484, BR15566_MidHaulDaysPerMonthLimit br15566,
											ICommonBusinessProcess commonBusinessProcess, IPartyRoleInRiskService partyRoleInRiskService,
											IPartyService partyService, IAdditionalInterestRoleService additionalInterestRoleService,
											IInsuranceRiskService insuranceRiskService) {
		super(vehicleMakesByYearService, vehicleModelsService, dateManagerService, vehicleDetailService, commonBusinessProcess,
				partyRoleInRiskService, partyService, additionalInterestRoleService, insuranceRiskService);
		this.retrieveVehicleRateGroupService = retrieveVehicleRateGroupService;
		this.br0616 = br0616;
		this.br15484 = br15484;
		this.br15566 = br15566;
	}

	@Override
	public void setVehicleDetail(Vehicle aVehicle,
								 Locale locale,
								 Long aPolicyVersionId,
								 DistributionChannelCodeEnum distributionChannel,
								 InsuranceBusinessCodeEnum insuranceBusiness) {

		String vehicleCode = aVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleRepositoryEntry().getVehicleCode();
		String vehicleYear = aVehicle.getVehicleDetailSpecificationRepositoryEntry().getVehicleYear().toString();
		Province province = Province.fromLocale(locale);
		PolicyVersion policyVersion = this.getPolicyVersion(aPolicyVersionId);
		ManufacturerCompanyCodeEnum manufacturerCompanyCodeEnum = policyVersion.getInsurancePolicy().getManufacturerCompany();
		Date ratingDate = this.dateManagerService.getReferenceDate(DateHelperEnum.FOR_FIRST_RATING, aPolicyVersionId);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String effectiveDate = sdf.format(ratingDate);

		Map<String, String> result =
				this.retrieveVehicleRateGroupService.executeService(manufacturerCompanyCodeEnum.getCode(), province.getCode(),
						vehicleCode.substring(0, 4).replaceFirst("^0", ""),
						vehicleYear, "", effectiveDate
				);

		aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRestrictedVehicleIndicator(false);

		if (result != null && !result.isEmpty()) {
			aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRateGroupClearCollision(result.get("COLL"));
			aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRateGroupClearDcpd(result.get("DCPD"));
			aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRateGroupClearComprehensive(result.get("COMP"));
			aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRateGroupClearAccidentBenefit(result.get("ACCB"));
		}

		/*
		 * As a there will be a rewrite of the AQ/QQ Serge Duchesne has provided the following patch/solution
		 * The Classic service PHYSICAL_OBJECT.GET_DETAILED_VEHICLE_TABLE_INFORMATIONx10.00 is called with DistributorChannel = DirectSeller
		 * to get retailPriceWithGST of vehicle
		 */
		VehicleDetail details = this.getVehicleDetailToVehicleDetailSpecificationRepositoryEntry(locale, aVehicle, aPolicyVersionId,
				DistributionChannelCodeEnum.DIRECT_SELLER, insuranceBusiness);
		if (details != null) {
			aVehicle.getVehicleDetailSpecificationRepositoryEntry().setRetailPriceWithGst(details.getRetailPriceWithGst());
		}
	}

    @Override
	public List<RuleExceptionResult> validateQuickQuoteSoftRoadblock(Vehicle aVehicle, Province aProvince, Language aLanguage) {

		List<RuleExceptionResult> result = new ArrayList<>();
		RuleExceptionResult br0616 = this.br0616.validate(aVehicle);
		if (br0616.hasFailed()) {
			result.add(br0616);
		}
		result.add(this.br15484.validate(aVehicle));
		result.add(this.br15566.validate(aVehicle));
		return result;
	}
}
