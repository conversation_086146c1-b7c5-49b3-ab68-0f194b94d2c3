package intact.lab.autoquote.backend.services.business.common.impl;

import com.ing.canada.cif.domain.IClient;
import com.ing.canada.cif.service.IClientService;
import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.UserTypeCodeEnum;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.service.IPolicyVersionService;
import intact.lab.autoquote.backend.services.business.common.IBusinessTransactionLoggingService;
import intact.lab.autoquote.backend.services.business.common.ICommonBusinessProcess;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Component
public class CommonBusinessProcess implements ICommonBusinessProcess {

    private final IPartyHelper partyHelper;
    private final IClientService clientService;
    private final IBusinessTransactionLoggingService bussTransLoggingService;
    private final IQuotationService quotationService;
    private final IPolicyVersionService policyVersionService;

    private static final Logger LOG = ESAPI.getLogger(CommonBusinessProcess.class);

    public CommonBusinessProcess(IPartyHelper partyHelper, IClientService clientService,
                                 IBusinessTransactionLoggingService bussTransLoggingService, IQuotationService quotationService,
                                 IPolicyVersionService policyVersionService) {
        this.partyHelper = partyHelper;
        this.clientService = clientService;
        this.bussTransLoggingService = bussTransLoggingService;
        this.quotationService = quotationService;
        this.policyVersionService = policyVersionService;
    }

    /**
     * @see ICommonBusinessProcess#getQuoteCalculationDetails(PolicyVersion
     *      aPolicyVersion, ProvinceCodeEnum aProvinceCode)
     */
    @Override
    public QuoteCalculationDetails getQuoteCalculationDetails(final PolicyVersion aPolicyVersion, final ProvinceCodeEnum aProvinceCode) {
        return this.quotationService.getQuoteCalculationDetails(aPolicyVersion, aProvinceCode);
    }

    @Override
    public IClient getCifClient(final PolicyVersion policyVersion) {

        Party party = this.partyHelper.getNamedInsured(policyVersion);

        IClient client = null;
        if (party != null && party.getCifClientId() != null) {
            client = this.clientService.getClientById(party.getCifClientId());
        }

        return client;
    }

    @Override
    public void createActivity(final PolicyVersion policyVersion, BusinessTransactionActivityCodeEnum trxActivity, UserTypeCodeEnum userType) {
        Assert.notNull(policyVersion, "PolicyVersion parameter is required for createActivity");
        Assert.notNull(trxActivity, "BusinessTransactionActivityCodeEnum parameter is required for createActivity");
        Assert.notNull(userType, "UserTypeCodeEnum parameter is required for createActivity");

        IClient cifClient = this.getCifClient(policyVersion);
        String cifClientNo = cifClient != null ? cifClient.getClientNo() : null;

        // TODO - create new createActivity signature to pass the policyVersion directly.
        this.bussTransLoggingService.createActivity(trxActivity, policyVersion.getId(), cifClientNo, null, userType);
    }

    @Override
    public PolicyVersion loadPolicyVersion(final Long aPolicyVersionId) {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, new StringBuilder(">> load aPolicyVersionId='").append(aPolicyVersionId).append("'").toString());
        }

        PolicyVersion policyVersion = this.policyVersionService.findById(aPolicyVersionId);
        if (policyVersion == null) {
            // If we can't find the policyVersion then there as been a rollback and the policy is out of sync,
            // we want to bring back the user to a proper state
            throw new OptimisticLockingFailureException("No policy version has been found by PolicyVersionId : " + aPolicyVersionId);
        }

        return policyVersion;
    }

    @Override
    @Transactional(value = "transactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void savePolicyVersion(final PolicyVersion policyVersion) {
        if (LOG.isDebugEnabled()) {
            LOG.debug(Logger.EVENT_SUCCESS, ">> saving policy version: id='" + policyVersion.getId() +
                    "', agreementNbr=" + policyVersion.getInsurancePolicy().getAgreementNumber() +
                    "'");
        }
        this.policyVersionService.persistCascadeAll(policyVersion);
    }
}
