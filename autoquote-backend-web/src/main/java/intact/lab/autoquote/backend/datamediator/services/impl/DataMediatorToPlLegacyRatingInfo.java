package intact.lab.autoquote.backend.datamediator.services.impl;

import com.ing.canada.common.exception.SystemException;
import com.ing.canada.plp.dao.base.IBaseEntityDAO;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.party.Address;
import com.ing.canada.plp.domain.party.GeographicalAssessment;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.som.sdo.contactpoint.AddressBO;
import com.ing.canada.som.sdo.place.GeographicalAssessmentBO;
import com.ing.canada.som.sdo.risk.InsuranceRiskBO;
import com.ing.canada.som.sdo.risk.LegacyRatingInfoByPostalCodeBO;
import com.ing.canada.som.sdo.risk.impl.LegacyRatingInfoByPostalCodeBOImpl;
import commonj.sdo.DataObject;
import intact.lab.autoquote.backend.datamediator.DMConstants;
import intact.lab.autoquote.backend.datamediator.services.IDataMediatorToPlLegacyRatingInfo;
import intact.lab.autoquote.backend.datamediator.utils.DataMediatorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Transactional
@Component
@Slf4j
public class DataMediatorToPlLegacyRatingInfo extends BaseMediatorToPL implements IDataMediatorToPlLegacyRatingInfo {

    public DataMediatorToPlLegacyRatingInfo(IBaseEntityDAO baseEntityDAO) {
        super(baseEntityDAO);
    }

    public void processLegacyRatingInfoByPostalCodeBO(PolicyVersion plPolicyVersion, DataObject dataObject) {
        if (plPolicyVersion != null && dataObject != null) {
            LegacyRatingInfoByPostalCodeBO legacyRatingInfoByPostalCodeBo = (LegacyRatingInfoByPostalCodeBOImpl)dataObject;
            InsuranceRiskBO somInsuranceRisk = legacyRatingInfoByPostalCodeBo.getTheInsuranceRiskBO();
            if (somInsuranceRisk == null) {
                log.error("SomInsuranceRisk unexepectedly null for a policy version = [" + plPolicyVersion.getId() + "]");
                throw new SystemException("SomInsuranceRisk unexepectedly null for a policy version = [" + plPolicyVersion.getId() + "]");
            }

            InsuranceRisk insuranceRisk = null;
            if (StringUtils.isNotEmpty(somInsuranceRisk.getPersistenceUniqueId())) {
                String insuranceRiskId = somInsuranceRisk.getPersistenceUniqueId();
                insuranceRisk = (InsuranceRisk)this.getBeanFromPLGraphWithCriteria(Long.valueOf(insuranceRiskId), InsuranceRisk.class, plPolicyVersion);
            }

            if (insuranceRisk == null) {
                log.error("InsuranceRisk unexepectedly null for a policy version and an InsuranceRiskSequence = [" + somInsuranceRisk.getInsuranceRiskSequence() + "]");
                throw new SystemException("InsuranceRisk is null for a policy version and an InsuranceRiskSequence = [" + somInsuranceRisk.getInsuranceRiskSequence() + "]");
            }

            DataMediatorUtils.setAttributesByReflectionFromSOMtoPL(DMConstants.plLegacyRatingInfoByPostalCodes, DMConstants.somLegacyRatingInfoByPostalCodes, insuranceRisk, dataObject);
        }
    }

    public void processGeographicalAssessment(PolicyVersion plPolicyVersion, DataObject dataObject) {
        if (plPolicyVersion != null && dataObject != null) {
            GeographicalAssessmentBO geographicalAssessment = (GeographicalAssessmentBO)dataObject;
            AddressBO somAddress = geographicalAssessment.getTheAddressBO();
            if (somAddress == null) {
                log.error("SomAddress unexepectedly null for a policy version = [" + plPolicyVersion.getId() + "]");
                throw new SystemException("SomAddress unexepectedly null for a policy version = [" + plPolicyVersion.getId() + "]");
            }

            Address address = null;
            if (StringUtils.isNotEmpty(somAddress.getPersistenceUniqueId())) {
                String addressID = somAddress.getPersistenceUniqueId();
                address = (Address)this.getBeanFromPLGraphWithCriteria(Long.valueOf(addressID), Address.class, plPolicyVersion);
            }

            if (address == null) {
                log.error("Address unexepectedly null for a policy version and an Addressid = [" + somAddress.getPersistenceUniqueId() + "]");
                throw new SystemException("Address is null for a policy version and an addressid = [" + somAddress.getPersistenceUniqueId() + "]");
            }

            GeographicalAssessment plGeographicalAssessment = new GeographicalAssessment();
            address.setGeographicalAssessment(plGeographicalAssessment);
            plGeographicalAssessment.setAddress(address);
            DataMediatorUtils.setAttributesByReflectionFromSOMtoPL(DMConstants.plGeographicalAssessmentNames, DMConstants.somGeographicalAssessmentNames, plGeographicalAssessment, dataObject);
        }

    }
}
