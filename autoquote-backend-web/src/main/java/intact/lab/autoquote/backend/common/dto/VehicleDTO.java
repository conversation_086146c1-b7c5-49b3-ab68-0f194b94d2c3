package intact.lab.autoquote.backend.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import intact.lab.autoquote.backend.common.enums.TrackingSystemCodeEnum;
import lombok.Data;
import org.joda.time.LocalDate;

@Data
public class VehicleDTO extends RiskDTO {

    private Integer year;
    private String make;
    private String model;
    private String modelCode;
    private Boolean usageModified;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private LocalDate purchasedDate;

    private Integer businessKmPerYear;
    private Integer kmPerYear;
    private Boolean trackingSystemInd;
    private TrackingSystemCodeEnum trackingSystemCode;
    private Integer normalRadiusKm;
    private Double grossVehicleWeight;
    private String useOfVehicleCode;
    private Integer midHaulingDaysPerMonth;
    private Boolean multiVehicleDiscountEligibilityInd;
    private Integer maximumRadiusKm;
}
