/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.services.business.usage;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.exception.RuleExceptionResult;
import intact.lab.autoquote.backend.common.exception.NoHitException;

/**
 * The Interface IUsageBusinessProcess.
 * 
 * <AUTHOR>
 */
public interface IUsageBusinessProcess {

	/**
	 * Save the Usage Information.
	 * 
	 * @param aPolicyVersion the policyVersion
	 */
	void save(PolicyVersion aPolicyVersion);

	/**
	 * Complete model PolicyHolder
	 *
	 * @param policyVersion {@link PolicyVersion}
	 * @param performDBOperation indicator to perform the DBO operation.
	 */
	void completeModelPolicyHolder(PolicyVersion policyVersion, boolean performDBOperation);

	void completeModelPartyRelatations(PolicyVersion policyVersion);

	/**
	 * Validate for the honeypot trap. Process it the same has a hard roadblock.
	 *
	 * @param trap the value to validate
	 * @return {@link RuleExceptionResult}
	 */
	RuleExceptionResult validateHoneypotTrap(final String trap);

	/**
	 * Assignation of the Occasional Drivers - BR368
	 *
	 * @param aPolicyVersion the a policy version
	 */
	void assignDrivers(PolicyVersion aPolicyVersion);

	/**
	 * Verify if we need to send the no hit page to the user
	 *
	 * @param aPolicyVersion the a policy version
	 * @throws NoHitException the no hit exception
	 */
	void processTheNoHitException(PolicyVersion aPolicyVersion) throws NoHitException;
}
