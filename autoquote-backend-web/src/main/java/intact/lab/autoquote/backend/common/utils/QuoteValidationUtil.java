package intact.lab.autoquote.backend.common.utils;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.com.CommunicationObjectModel;
import com.intact.com.enums.ComErrorCodeEnum;
import com.intact.com.state.ComState;
import com.intact.com.transaction.activity.ComEvent;
import com.intact.com.transaction.activity.enums.ComEventEnum;
import com.intact.com.util.ComValidationError;

import java.util.Date;

public class QuoteValidationUtil {

    public static CommunicationObjectModel validatePolicyVersion(String uuid, PolicyVersion policyVersion, CommunicationObjectModel retCOM) {
        String comProvince = retCOM.getContext().getProvince().getCode();

        if (policyVersion == null) {
            policyIsEmpty(uuid, retCOM);
            // Verify if province are the same in both context and policyVersion
        } else if (!comProvince.equals(policyVersion.getInsurancePolicy().getManufacturingContext().getProvince().getCode())) {
            policyProvinceIsDifferent(retCOM);
        } else {
            retCOM.getState().setTransactionStatus(ComState.ComTransactionStatus.FOUND);
            validateExpiredPolicy(policyVersion, retCOM);
        }

        return retCOM;
    }

    private static void policyIsEmpty(String uuid, CommunicationObjectModel retCOM) {

        retCOM.setUuId(uuid); // uuid stays as provided
        retCOM.setAgreementNumber(null); // ensure agreement number undefined

        ComEvent comEvent = new ComEvent();
        comEvent.setEventCode(ComEventEnum.RETRIEVE_QUOTE);
        retCOM.setComEvent(comEvent);

        retCOM.getState().setTransactionStatus(ComState.ComTransactionStatus.NOT_FOUND);

    }

    private static void policyProvinceIsDifferent(CommunicationObjectModel retCOM){
        // when province of policyVersion is different from provided context
        ComValidationError sIdError = new ComValidationError();
        sIdError.setErrorCode(ComErrorCodeEnum.INVALID_PROVINCE);
        // update province in context
        retCOM.getValidationErrors().add(sIdError);

        retCOM.getState().setTransactionStatus(ComState.ComTransactionStatus.NOT_FOUND);

    }

    private static void validateExpiredPolicy(PolicyVersion policyVersion, CommunicationObjectModel retCOM) {

        Date today = new Date();
        Date returnedDate = policyVersion.getInsurancePolicy().getQuotationValidityExpiryDate();
        if(returnedDate.before(today)) {
            ComValidationError sIdError = new ComValidationError();
            sIdError.setErrorCode(ComErrorCodeEnum.EXPIRED_QUOTE);
            retCOM.getValidationErrors().add(sIdError);
        }
    }
}
