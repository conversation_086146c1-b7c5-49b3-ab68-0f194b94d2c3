package intact.lab.autoquote.backend.config;

import com.fasterxml.jackson.datatype.joda.JodaModule;
import com.ing.canada.cif.helpers.EncoderHelper;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.service.impl.SubBrokersService;
import com.ing.canada.common.services.impl.ilservices.base.GenericDelegate;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleMakesByYearService;
import com.ing.canada.common.services.impl.ilservices.vehicle.VehicleModelsService;
import com.ing.canada.common.util.ReloadableProperties;
import com.ing.canada.plp.domain.enums.UBIProviderCodeEnum;
import com.intact.canada.common.services.api.form.IFormFieldValidatorService;
import com.intact.canada.common.services.impl.java.form.FormFieldValidatorService;
import com.intact.globaladmin.dao.IDocumentDAO;
import com.intact.globaladmin.dao.IWebAttackDAO;
import com.intact.globaladmin.dao.impl.DocumentDAO;
import com.intact.globaladmin.dao.impl.WebAttackDAO;
import intact.lab.autoquote.backend.services.rating.impl.GenericPegaService;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;

@Configuration
@PropertySource("classpath:capi-services.properties")
public class ApiConfig {

    private static final String API_TITLE = "Autoquote-Backend";
    private static final String API_DESC = "Autoquote Backend Rest for IRCA";
    private static final String API_VERSION = "1";

    @Value("${pega-application-id}")
    private String pegaApplicationId;

    @Value("${environnement-level}")
    private String environnementLevel;

    @Bean
    OpenAPI openApi() {
        return new OpenAPI().info(new Info().title(API_TITLE).description(API_DESC).version(API_VERSION));
    }

    @Bean(name = "application-id")
    public String applicationId() {
        return "BWAQ";
    }

    @Bean(name = "il-timeout")
    public int ilTimeout() {
        return 30000;
    }

    @Bean(name = "pega-application-id")
    public String pegaApplicationId() {
        return pegaApplicationId;
    }

    @Bean("webmethods-endpoint")
    public String webMethodsEndpoint(@Value("${webmethods-endpoint}") String webMethodsEndpoint) {
        return webMethodsEndpoint;
    }

    @Bean
    public UBIProviderCodeEnum ubiProvider() {
        return UBIProviderCodeEnum.valueOf("TRUE_MOTION");
    }

    @Bean
    public ReloadableProperties reloadableHolidaysConfiguration() {
        ReloadableProperties reloadableProperties = new ReloadableProperties();
        reloadableProperties.setCacheSeconds(120);
        reloadableProperties.setFilename("holidays.properties");
        return reloadableProperties;
    }

    @Bean
    public VehicleMakesByYearService vehicleMakesByYearService() {
        return new VehicleMakesByYearService();
    }

    @Bean
    public VehicleModelsService vehicleModelsService() {
        return new VehicleModelsService();
    }

    @Bean(name = "reloadableResourceBundleMessageSource")
    public ReloadableResourceBundleMessageSource reloadableResourceBundleMessageSource() {
        return new ReloadableResourceBundleMessageSource();
    }

    @Bean(name = "reloadableRatingConfiguration")
    public ReloadableResourceBundleMessageSource reloadableRatingConfiguration() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setCacheSeconds(120);
        messageSource.setBasename("classpath:autoquote-rating");
        messageSource.setFallbackToSystemLocale(true);
        return messageSource;
    }

    @Bean(name = "cifSubBrokersService")
    @Primary
    public ISubBrokersService subBrokersService() {
        return new SubBrokersService();
    }

    @Bean(name = "cifEncoderHelper")
    public EncoderHelper encoderHelper() {
        return new EncoderHelper();
    }

    @Bean(name = "environnement-level")
    public String environnementLevel() {
        return environnementLevel;
    }

    @Bean
    public JodaModule jodaTimeModule() {
        return new JodaModule();
    }

    @Bean
    public IFormFieldValidatorService formFieldValidatorService() {
        return new FormFieldValidatorService();
    }

    @Bean(name = "getCreditScore_5x00")
    public GenericDelegate getCreditScoreDelegate() {
        GenericDelegate delegate = new GenericDelegate();
        delegate.setComponentName("PARTY");
        delegate.setServiceName("GET_CREDIT_SCORE");
        delegate.setServiceVersion("5.00");
        return delegate;
    }

    @Bean(name = "determineQuoteInfos_0x00")
    public GenericPegaService determineQuoteInfosService() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("RATING");
        service.setServiceName("DETERMINE_QUOTE_INFOS");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "manageProducts0x00")
    public GenericPegaService manageProducts0x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("PRODUCT");
        service.setServiceName("MANAGE_PRODUCTS");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "codePlPolicyWest0x00")
    public GenericPegaService codePlPolicyWest0x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("UNDERWRITING");
        service.setServiceName("CODE_PL_POLICY_WEST");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "ratePlPolicyWest0x00")
    public GenericPegaService ratePlPolicyWest0x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("RATING");
        service.setServiceName("RATE_PL_POLICY_WEST");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "codePlPolicyCar0x00")
    public GenericPegaService codePlPolicyCar0x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("UNDERWRITING");
        service.setServiceName("CODE_PL_POLICY_CAR");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "ratePlPolicyCar0x00")
    public GenericPegaService ratePlPolicyCar0x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("RATING");
        service.setServiceName("RATE_PL_POLICY_CAR");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "codePlPolicyIntact0x00")
    public GenericPegaService codePlPolicyIntact0x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("UNDERWRITING");
        service.setServiceName("CODE_PL_POLICY_INTACT");
        service.setServiceVersion("0.00");
        return service;
    }

    @Bean(name = "getAnnualPremiumCoverages31x00")
    public GenericPegaService getAnnualPremiumCoverages31x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("BUSINESS_RULES");
        service.setServiceName("GET_ANNUAL_PREMIUM_COVERAGES_ON_RATING_RISK");
        service.setServiceVersion("31.00");
        return service;
    }

    @Bean(name = "getMultiplicativeRatingFactors2x00")
    public GenericPegaService getMultiplicativeRatingFactors2x00() {
        GenericPegaService service = new GenericPegaService();
        service.setComponentName("RATING");
        service.setServiceName("GET_MULTIPLICATIVE_RATING_FACTORS");
        service.setServiceVersion("2.00");
        return service;
    }

    @Bean(name = "GlobalDocumentDAO")
    public IDocumentDAO documentDAO() {
        return new DocumentDAO();
    }

    @Bean(name = "GlobalWebAttackDAO")
    public IWebAttackDAO webAttackDAO() {
        return new WebAttackDAO();
    }
}
