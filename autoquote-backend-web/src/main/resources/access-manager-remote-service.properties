# ---------------------------------------------------------------------
# Use this configuration to run access manager on your local computer
#
# With SSL, update the url and uncomment the keys
#   - https://localhost:9443/...
#   - HTTPS_AUTHENTICATION_PROTOCOL, KEYSTORE_xxx and STRUTSTORE_xxx
# ---------------------------------------------------------------------
#ServiceInvocationHandler=com.ing.canada.commons.service.factory.HttpServiceInvocationHandler
#com.ing.canada.singleid.accessmanager.service.IClientAccountService=http://localhost:9088/access-manager-web/remoting/ClientAccountService
#com.ing.canada.singleid.accessmanager.service.IUserAccountService=http://localhost:9088/access-manager-web/remoting/UserAccountService
# -- end

# ---------------------------------------------------------------------
# Use this configuration to call access manager from a remote computer
#
#   - KEYSTORE_URL: pointe sur le keystore qui contient le certificat de
#     l'application (autorisï¿½ par l'accï¿½s manager)
#
#   - TRUSTSTORE_URL= contient le cacerts des serveurs trustï¿½s par portfolio.
#     (l'access manager)
# ---------------------------------------------------------------------
ServiceInvocationHandler=com.ing.canada.commons.service.factory.CommonsHttpServiceInvocationHandler
com.ing.canada.singleid.accessmanager.service.IClientAccountService=https://uat-accessmanager-intact.iad.ca.inet:55464/access-manager-web/remoting/ClientAccountService
com.ing.canada.singleid.accessmanager.service.IUserAccountService=https://uat-accessmanager-intact.iad.ca.inet:55464/access-manager-web/remoting/UserAccountService

HTTPS_AUTHENTICATION_PROTOCOL=SSL_CLIENT_AUTHENTICATION

KEYSTORE_URL=C:/RAD7/access-manager-intact-uat/keystore-accessmanager-intact.jks
KEYSTORE_PASSWORD=uatkey
TRUSTSTORE_URL=C:/RAD7/access-manager-intact-uat/truststore-accessmanager-intact.jks
TRUSTSTORE_PASSWORD=uattru