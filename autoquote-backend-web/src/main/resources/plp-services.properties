####
# This file is used by plp-services library itself (see pom.xml) to configure the connection to the PLP database.
# PLP being an old project, we do not connect through a REST API like for XPAS but directly to the database.
dataSource.jndi-name=java:comp/env/jdbc/plpolicy
hibernateAdapter.showSql=false
#database.plp.url=********************************************************************************************************************* = DEDICATED)(SERVICE_NAME=dpsintg.intact.net)))
hibernateAdapter.databasePlatform=org.hibernate.dialect.Oracle12cDialect
hibernateAdapter.generateDdl=false
optimistic.group.lock.enabled=false
hibernateAdapter.jta_platform=org.hibernate.engine.transaction.jta.platform.internal.AtomikosJtaPlatform

hibernateAdapter.format_sql=false
hibernateAdapter.use_sql_comments=false
hibernateAdapter.fetchSize=10
hibernateAdapter.batchSize=10
hibernateAdapter.default_batch_fetch_size=10

# 128 bit encryption key. 32 hexadecimal characters.
IngAesCipher.key=A61B6401E2F362C97B4D7A37AE6301BC
