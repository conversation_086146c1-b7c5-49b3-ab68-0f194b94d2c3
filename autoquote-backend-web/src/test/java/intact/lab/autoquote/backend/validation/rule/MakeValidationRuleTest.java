package intact.lab.autoquote.backend.validation.rule;

import intact.lab.autoquote.backend.common.dto.MakeDTO;
import intact.lab.autoquote.backend.common.dto.VehicleDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import intact.lab.autoquote.backend.facade.IVehicleFacade;
import intact.lab.autoquote.backend.validation.ValidationTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MakeValidationRuleTest {

	@InjectMocks
	private MakeValidationRule validationRule;

	@Mock
	private IVehicleFacade vehicleFacade;

	private Errors errors;

	@BeforeEach
	public void setup() {
		this.errors = new BeanPropertyBindingResult(new VehicleDTO(), "vehicle");
	}

	@Test
	public void testValidate_ValidDomain_ShouldPass() {
		List<MakeDTO> validMakes = buildMakeDomain("hyundai");

		when(this.vehicleFacade.getVehicleMakeList(anyString(), anyString(), anyString())).thenReturn(validMakes);

		this.validationRule.validate("hyundai", null, 2020, "QC", "EN", this.errors);
		ValidationTestUtils.assertNoErrors(this.errors);
	}

	@Test
	public void testValidate_InvalidDomain_ShouldFail() {
		List<MakeDTO> validMakes = buildMakeDomain("tesla");

		when(this.vehicleFacade.getVehicleMakeList(anyString(), anyString(), anyString())).thenReturn(validMakes);

		this.validationRule.validate("hyundai", null, 2020, "QC", "EN", this.errors);
		ValidationTestUtils.assertHasError(this.errors, "make", BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode());
	}

	private List<MakeDTO> buildMakeDomain(String value) {
		List<MakeDTO> validValues = new ArrayList<>();
		MakeDTO vv = new MakeDTO(value, value);
		validValues.add(vv);
		return validValues;
	}
}
