/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.claim.Claim;
import com.ing.canada.som.interfaces.claim.KindOfLoss;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.risk.InsuranceRisk;

import java.util.List;

/**
 * 
 * Mock class for a SOM Kind of loss. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements {@link KindOfLoss}
 * 
 * <AUTHOR>
 *
 */
public class MockKindOfLoss implements KindOfLoss {
	String atFaultInd;

	String claimConsideredDone;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public String getKindOfLoss() {
		return null;
	}

	@Override
	public void setKindOfLoss(String newKindOfLoss) {
		// noop
	}

	@Override
	public String getSubFileType() {
		return null;
	}

	@Override
	public void setSubFileType(String newSubFileType) {
		// noop
	}

	@Override
	public String getCoverageType() {
		return null;
	}

	@Override
	public void setCoverageType(String newCoverageType) {
		// noop
	}

	@Override
	public String getCoverageCode() {
		return null;
	}

	@Override
	public void setCoverageCode(String newCoverageCode) {
		// noop
	}

	@Override
	public Double getAmountPaid() {
		return null;
	}

	@Override
	public void setAmountPaid(Double newAmountPaid) {
		// noop
	}

	@Override
	public String getAtFaultInd() {
		return this.atFaultInd;
	}

	@Override
	public void setAtFaultInd(String newAtFaultInd) {
		this.atFaultInd = newAtFaultInd;
	}

	@Override
	public String getClaimConsideredCode() {
		return this.claimConsideredDone;
	}

	@Override
	public void setClaimConsideredCode(String newClaimConsideredCode) {
		this.claimConsideredDone = newClaimConsideredCode;
	}

	@Override
	public Double getReserveAmount() {
		return null;
	}

	@Override
	public void setReserveAmount(Double newReserveAmount) {
		// noop
	}

	@Override
	public Double getAmountIncurred() {
		return null;
	}

	@Override
	public void setAmountIncurred(Double newAmountIncurred) {
		// noop
	}

	@Override
	public String getRelevantForNumberOfClaimInd() {
		return null;
	}

	@Override
	public void setRelevantForNumberOfClaimInd(String newRelevantForNumberOfClaimInd) {
		// noop
	}

	@Override
	public Double getRecoveryAmount() {
		return null;
	}

	@Override
	public void setRecoveryAmount(Double newRecoveryAmount) {
		// noop
	}

	@Override public String getInjuryType() {
		return null;
	}

	@Override public void setInjuryType(String s) {

	}

	@Override public String getExposureType() {
		return null;
	}

	@Override public void setExposureType(String s) {

	}

	@Override
	public Claim getTheClaim() {
		return null;
	}

	@Override
	public void setTheClaim(Claim newTheClaim) {
		// noop
	}

	@Override
	public Claim createTheClaim() {
		return null;
	}

	@Override
	public Claim createTheClaim(Class<? extends Claim> theInterface) {
		return null;
	}

	@Override
	public KindOfLoss getTheKindOfLossPriorTrans() {
		return null;
	}

	@Override
	public void setTheKindOfLossPriorTrans(KindOfLoss newTheKindOfLossPriorTrans) {
		// noop
	}

	@Override
	public KindOfLoss createTheKindOfLossPriorTrans() {
		return null;
	}

	@Override
	public KindOfLoss createTheKindOfLossPriorTrans(Class<? extends KindOfLoss> theInterface) {
		return null;
	}

	@Override public InsuranceRisk getTheInsuranceRiskOriginal() {
		return null;
	}

	@Override public void setTheInsuranceRiskOriginal(InsuranceRisk insuranceRisk) {

	}

	@Override public InsuranceRisk createTheInsuranceRiskOriginal() {
		return null;
	}

	@Override public InsuranceRisk createTheInsuranceRiskOriginal(Class<? extends InsuranceRisk> aClass) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public String getCoverageCodeFromAllPerilsInd() {
		return null;
	}

	@Override
	public void setCoverageCodeFromAllPerilsInd(String arg0) {
		// noop
	}

}
