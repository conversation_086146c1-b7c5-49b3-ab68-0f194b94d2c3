/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package intact.lab.autoquote.backend.mocks;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.som.base.SOMBaseObjectInterface;
import com.ing.canada.som.interfaces.agreement.InsurancePolicy;
import com.ing.canada.som.interfaces.agreement.PolicyVersion;
import com.ing.canada.som.interfaces.agreement.PolicyVersionSummary;
import com.ing.canada.som.interfaces.businessModel.AssessmentResult;
import com.ing.canada.som.interfaces.businessModel.ManufacturingContext;
import com.ing.canada.som.interfaces.businessTransaction.TransactionalMessage;
import com.ing.canada.som.interfaces.documentManagement.Document;
import com.ing.canada.som.interfaces.note.Note;
import com.ing.canada.som.interfaces.partyRoleInAgreement.Manufacturer;

import java.util.GregorianCalendar;
import java.util.List;

/**
 * 
 * Mock class for a SOM InsurancePolicy. Created because of lack of an implementation that can be mocked and because
 * Mockito/Powermock can't mock the interface. Implements
 * {@link InsurancePolicy}
 * 
 * <AUTHOR>
 *
 */
public class MockInsurancePolicy implements InsurancePolicy {

	private String applicationMode;

	private String externalSystemOrigin;

	private GregorianCalendar originalInceptionDate;

	@Override
	public String getActionTaken() {
		return null;
	}

	@Override
	public void setActionTaken(String newActionTaken) {
		// noop
	}

	@Override
	public String getPersistenceUniqueId() {
		return null;
	}

	@Override
	public void setPersistenceUniqueId(String newPersistenceUniqueId) {
		// noop
	}

	@Override
	public String getTestDataTrace() {
		return null;
	}

	@Override
	public void setTestDataTrace(String newTestDataTrace) {
		// noop
	}

	@Override public String getTestDataTraceFormatted() {
		return null;
	}

	@Override public void setTestDataTraceFormatted(String s) {

	}

	@Override
	public void clearTheDocument() {
		// noop
	}

	@Override
	public List<Document> getTheDocument() {
		return null;
	}

	@Override
	public Document getTheDocument(String uniqueId) {
		return null;
	}

	@Override
	public Document getTheDocument(int index) {
		return null;
	}

	@Override
	public Document addTheDocument() {
		return null;
	}

	@Override
	public Document addTheDocument(Class<? extends Document> theInterface) {
		return null;
	}

	@Override
	public void addTheDocument(Document newTheDocument) {
		// noop
	}

	@Override
	public void addTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(int index, Document newTheDocument) {
		// noop
	}

	@Override
	public void setTheDocument(List<Document> objList) {
		// noop
	}

	@Override
	public void removeTheDocument(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheDocument(int index) {
		// noop
	}

	@Override
	public void clearTheAssessmentResult() {
		// noop
	}

	@Override
	public List<AssessmentResult> getTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(String uniqueId) {
		return null;
	}

	@Override
	public AssessmentResult getTheAssessmentResult(int index) {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult() {
		return null;
	}

	@Override
	public AssessmentResult addTheAssessmentResult(Class<? extends AssessmentResult> theInterface) {
		return null;
	}

	@Override
	public void addTheAssessmentResult(AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void addTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(int index, AssessmentResult newTheAssessmentResult) {
		// noop
	}

	@Override
	public void setTheAssessmentResult(List<AssessmentResult> objList) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheAssessmentResult(int index) {
		// noop
	}

	@Override
	public void clearTheTransactionalMessage() {
		// noop
	}

	@Override
	public List<TransactionalMessage> getTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(String uniqueId) {
		return null;
	}

	@Override
	public TransactionalMessage getTheTransactionalMessage(int index) {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage() {
		return null;
	}

	@Override
	public TransactionalMessage addTheTransactionalMessage(Class<? extends TransactionalMessage> theInterface) {
		return null;
	}

	@Override
	public void addTheTransactionalMessage(TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void addTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(int index, TransactionalMessage newTheTransactionalMessage) {
		// noop
	}

	@Override
	public void setTheTransactionalMessage(List<TransactionalMessage> objList) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheTransactionalMessage(int index) {
		// noop
	}

	@Override
	public String getUniqueId() {
		return null;
	}

	@Override
	public void setUniqueId(String string) {
		// noop
	}

	@Override
	public boolean equals(SOMBaseObjectInterface _baseObject) {

		return false;
	}

	@Override
	public String getAgreementType() {
		return null;
	}

	@Override
	public void setAgreementType(String newAgreementType) {
		// noop
	}

	@Override
	public String getAgreementNumber() {
		return null;
	}

	@Override
	public void setAgreementNumber(String newAgreementNumber) {
		// noop
	}

	@Override
	public String getAgreementNumberTechnical() {
		return null;
	}

	@Override
	public void setAgreementNumberTechnical(String newAgreementNumberTechnical) {
		// noop
	}

	@Override
	public String getAgreementStatus() {
		return null;
	}

	@Override
	public void setAgreementStatus(String newAgreementStatus) {
		// noop
	}

	@Override
	public String getInputSystem() {
		return null;
	}

	@Override
	public void setInputSystem(String s) {

	}

	@Override
	public GregorianCalendar getOriginalInceptionDate() {
		return this.originalInceptionDate;
	}

	@Override
	public void setOriginalInceptionDate(GregorianCalendar newOriginalInceptionDate) {
		this.originalInceptionDate = newOriginalInceptionDate;
	}

	@Override
	public GregorianCalendar getPolicyFirstInceptionDate() {
		return null;
	}

	@Override
	public void setPolicyFirstInceptionDate(GregorianCalendar newPolicyFirstInceptionDate) {
		// noop
	}

	@Override
	public Integer getQuotationValidityPeriodInDays() {
		return null;
	}

	@Override
	public void setQuotationValidityPeriodInDays(Integer newQuotationValidityPeriodInDays) {
		// noop
	}

	@Override
	public GregorianCalendar getQuotationValidityExpiryDate() {
		return null;
	}

	@Override
	public void setQuotationValidityExpiryDate(GregorianCalendar newQuotationValidityExpiryDate) {
		// noop
	}

	@Override
	public String getRatingBasis() {
		return null;
	}

	@Override
	public void setRatingBasis(String newRatingBasis) {
		// noop
	}

	@Override
	public String getLineOfBusiness() {
		return null;
	}

	@Override
	public void setLineOfBusiness(String newLineOfBusiness) {
		// noop
	}

	@Override
	public String getSpfCode() {
		return null;
	}

	@Override
	public void setSpfCode(String newSpfCode) {
		// noop
	}

	@Override
	public String getTestDataInd() {
		return null;
	}

	@Override
	public void setTestDataInd(String newTestDataInd) {
		// noop
	}

	@Override
	public String getPersistenceInd() {
		return null;
	}

	@Override
	public void setPersistenceInd(String newPersistenceInd) {
		// noop
	}

	@Override
	public String getExternalSystemOrigin() {
		return this.externalSystemOrigin;
	}

	@Override
	public void setExternalSystemOrigin(String newExternalSystemOrigin) {
		this.externalSystemOrigin = newExternalSystemOrigin;
	}

	@Override
	public String getConversionCode() {
		return null;
	}

	@Override
	public void setConversionCode(String newConversionCode) {
		// noop
	}

	@Override
	public String getConversionType() {
		return null;
	}

	@Override
	public void setConversionType(String newConversionType) {
		// noop
	}

	@Override
	public Integer getPoolToUseForCallService() {
		return null;
	}

	@Override
	public void setPoolToUseForCallService(Integer newPoolToUseForCallService) {
		// noop
	}

	@Override
	public GregorianCalendar getQuotationValidityEffectiveDateSystem() {
		return null;
	}

	@Override
	public void setQuotationValidityEffectiveDateSystem(GregorianCalendar newQuotationValidityEffectiveDateSystem) {
		// noop
	}

	@Override
	public GregorianCalendar getQuotationValidityEffectiveDateModified() {
		return null;
	}

	@Override
	public void setQuotationValidityEffectiveDateModified(GregorianCalendar newQuotationValidityEffectiveDateModified) {
		// noop
	}

	@Override
	public GregorianCalendar getQuotationValidityEffectiveDate() {
		return null;
	}

	@Override
	public void setQuotationValidityEffectiveDate(GregorianCalendar newQuotationValidityEffectiveDate) {
		// noop
	}

	@Override
	public String getApplicationMode() {
		/**
		 * Return an application mode for test purposes. The mode attribute can be set in to allow different modes for
		 * certain tests.
		 */
		if (this.applicationMode == null) {
			return ApplicationModeEnum.REGULAR_QUOTE.getCode();
		}
		return this.applicationMode;
	}

	@Override
	public void setApplicationMode(String newApplicationMode) {
		/**
		 * Application mode can be set to a different value to be used for certain tests.
		 */
		this.applicationMode = newApplicationMode;
	}

	@Override
	public String getOtherInsuranceReason() {
		return null;
	}

	@Override
	public void setOtherInsuranceReason(String newOtherInsuranceReason) {
		// noop
	}

	@Override
	public String getOtherInsuranceType() {
		return null;
	}

	@Override
	public void setOtherInsuranceType(String newOtherInsuranceType) {

	}

	@Override
	public String getOtherInsuranceDeclaredInd() {
		return null;
	}

	@Override
	public void setOtherInsuranceDeclaredInd(String newOtherInsuranceDeclaredInd) {

	}

	@Override
	public String getRelatedInsurancePolicyValidationResult() {
		return null;
	}

	@Override
	public void setRelatedInsurancePolicyValidationResult(String s) {

	}

	@Override
	public String getSandboxInd() {
		return null;
	}

	@Override
	public void setSandboxInd(String s) {

	}

	@Override
	public String getSandboxExecutionMode() {
		return null;
	}

	@Override
	public void setSandboxExecutionMode(String s) {

	}

	@Override
	public PolicyVersion getThePolicyVersionQuote() {
		return null;
	}

	@Override
	public void setThePolicyVersionQuote(PolicyVersion newThePolicyVersionQuote) {
		// noop
	}

	@Override
	public PolicyVersion createThePolicyVersionQuote() {
		return null;
	}

	@Override
	public PolicyVersion createThePolicyVersionQuote(Class<? extends PolicyVersion> theInterface) {
		return null;
	}

	@Override
	public void clearThePolicyVersion() {
		// noop
	}

	@Override
	public List<PolicyVersion> getThePolicyVersion() {
		return null;
	}

	@Override
	public PolicyVersion getThePolicyVersion(String uniqueId) {
		return null;
	}

	@Override
	public PolicyVersion getThePolicyVersion(int index) {
		return null;
	}

	@Override
	public PolicyVersion addThePolicyVersion() {
		return null;
	}

	@Override
	public PolicyVersion addThePolicyVersion(Class<? extends PolicyVersion> theInterface) {
		return null;
	}

	@Override
	public void addThePolicyVersion(PolicyVersion newThePolicyVersion) {
		// noop
	}

	@Override
	public void addThePolicyVersion(int index, PolicyVersion newThePolicyVersion) {
		// noop
	}

	@Override
	public void setThePolicyVersion(int index, PolicyVersion newThePolicyVersion) {
		// noop
	}

	@Override
	public void setThePolicyVersion(List<PolicyVersion> objList) {
		// noop
	}

	@Override
	public void removeThePolicyVersion(String uniqueId) {
		// noop
	}

	@Override
	public void removeThePolicyVersion(int index) {
		// noop
	}

	@Override
	public ManufacturingContext getTheManufacturingContext() {
		return null;
	}

	@Override
	public void setTheManufacturingContext(ManufacturingContext newTheManufacturingContext) {
		// noop
	}

	@Override
	public ManufacturingContext createTheManufacturingContext() {
		return null;
	}

	@Override
	public ManufacturingContext createTheManufacturingContext(Class<? extends ManufacturingContext> theInterface) {
		return null;
	}

	@Override
	public InsurancePolicy getTheInsurancePolicyIsReplacing() {
		return null;
	}

	@Override
	public void setTheInsurancePolicyIsReplacing(InsurancePolicy newTheInsurancePolicyIsReplacing) {
		// noop
	}

	@Override
	public InsurancePolicy createTheInsurancePolicyIsReplacing() {
		return null;
	}

	@Override
	public InsurancePolicy createTheInsurancePolicyIsReplacing(Class<? extends InsurancePolicy> theInterface) {
		return null;
	}

	@Override
	public void clearThePolicyVersionSummary() {

	}

	@Override
	public List<PolicyVersionSummary> getThePolicyVersionSummary() {
		return null;
	}

	@Override
	public PolicyVersionSummary getThePolicyVersionSummary(String s) {
		return null;
	}

	@Override
	public PolicyVersionSummary getThePolicyVersionSummary(int i) {
		return null;
	}

	@Override
	public PolicyVersionSummary addThePolicyVersionSummary() {
		return null;
	}

	@Override
	public PolicyVersionSummary addThePolicyVersionSummary(Class<? extends PolicyVersionSummary> aClass) {
		return null;
	}

	@Override
	public void addThePolicyVersionSummary(PolicyVersionSummary policyVersionSummary) {

	}

	@Override
	public void addThePolicyVersionSummary(int i, PolicyVersionSummary policyVersionSummary) {

	}

	@Override
	public void setThePolicyVersionSummary(int i, PolicyVersionSummary policyVersionSummary) {

	}

	@Override
	public void setThePolicyVersionSummary(List<PolicyVersionSummary> list) {

	}

	@Override
	public void removeThePolicyVersionSummary(String s) {

	}

	@Override
	public void removeThePolicyVersionSummary(int i) {

	}

	@Override
	public void clearTheNote() {
		// noop
	}

	@Override
	public List<Note> getTheNote() {
		return null;
	}

	@Override
	public Note getTheNote(String uniqueId) {
		return null;
	}

	@Override
	public Note getTheNote(int index) {
		return null;
	}

	@Override
	public Note addTheNote() {
		return null;
	}

	@Override
	public Note addTheNote(Class<? extends Note> theInterface) {
		return null;
	}

	@Override
	public void addTheNote(Note newTheNote) {
		// noop
	}

	@Override
	public void addTheNote(int index, Note newTheNote) {
		// noop
	}

	@Override
	public void setTheNote(int index, Note newTheNote) {
		// noop
	}

	@Override
	public void setTheNote(List<Note> objList) {
		// noop
	}

	@Override
	public void removeTheNote(String uniqueId) {
		// noop
	}

	@Override
	public void removeTheNote(int index) {
		// noop
	}

	@Override
	public Manufacturer getTheManufacturer() {
		return null;
	}

	@Override
	public void setTheManufacturer(Manufacturer newTheManufacturer) {
		// noop
	}

	@Override
	public Manufacturer createTheManufacturer() {
		return null;
	}

	@Override
	public Manufacturer createTheManufacturer(Class<? extends Manufacturer> theInterface) {
		return null;
	}

	@Override
	public String getUuid() {
		return null;
	}

	@Override
	public void setUuid(String arg0) {
		// noop
	}

	@Override
	public GregorianCalendar getOriginalInceptionDateModified() {
		return null;
	}

	@Override
	public GregorianCalendar getOriginalInceptionDateSystem() {
		return null;
	}

	@Override
	public void setOriginalInceptionDateModified(GregorianCalendar arg0) {
		// noop
	}

	@Override
	public void setOriginalInceptionDateSystem(GregorianCalendar arg0) {
		// noop
	}

	@Override
	public String getPolicyInputSource() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setPolicyInputSource(String newPolicyInputSource) {
		// TODO Auto-generated method stub

	}

}
