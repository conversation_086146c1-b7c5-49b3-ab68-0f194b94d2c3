package intact.lab.autoquote.backend.facade.impl;

import com.intact.com.broker.ComBrokerInfo;
import com.intact.com.context.ComContext;
import intact.lab.autoquote.backend.common.dto.DistributorDTO;
import intact.lab.autoquote.backend.common.enums.WebSiteOriginEnum;
import intact.lab.autoquote.backend.common.exception.AutoQuoteException;
import intact.lab.autoquote.backend.converter.ICOMConverter;
import intact.lab.autoquote.backend.services.impl.BrokerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DistributorFacadeTest {

    @Mock
    private BrokerService brokerService;

    @Mock
    private ICOMConverter<DistributorDTO, ComBrokerInfo> comDistributorConverter;

    @InjectMocks
    private DistributorFacade distributorFacade;

    private String apiKey;
    private String language;
    private String province;
    private String postalCode;
    private String subBrokerNo;
    private String origin;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(distributorFacade, "comDistributorConverter", comDistributorConverter);
        initializeTestData();
    }

    private void initializeTestData() {
        apiKey = "testApiKey";
        language = "en";
        province = "ON";
        postalCode = "A1A1A1";
        subBrokerNo = "123";
        origin = WebSiteOriginEnum.INTACT.name();
    }

    @Test
    void RetrieveBrokerInfo_ShouldReturnDTO_WhenBrokerInfoExists() {
        ComBrokerInfo brokerInfo = new ComBrokerInfo();
        DistributorDTO expectedDTO = new DistributorDTO();

        when(brokerService.getBrokerInfo(any(ComContext.class))).thenReturn(brokerInfo);
        when(comDistributorConverter.toDTO(brokerInfo)).thenReturn(expectedDTO);

        DistributorDTO result = distributorFacade.retrieveBrokerInfo(apiKey, language, province, postalCode, subBrokerNo, origin);

        assertNotNull(result);
        assertEquals(expectedDTO, result);
        verify(brokerService, times(1)).getBrokerInfo(any(ComContext.class));
        verify(comDistributorConverter, times(1)).toDTO(brokerInfo);
    }

    @Test
    void RetrieveBrokerInfo_ShouldReturnEmptyDTO_WhenBrokerInfoIsNull() {
        when(brokerService.getBrokerInfo(any(ComContext.class))).thenReturn(null);

        DistributorDTO result = distributorFacade.retrieveBrokerInfo(apiKey, language, province, postalCode, subBrokerNo, origin);

        assertNotNull(result);
        verify(brokerService, times(1)).getBrokerInfo(any(ComContext.class));
        verify(comDistributorConverter, never()).toDTO(any());
    }

    @Test
    void RetrieveBrokerInfo_ShouldThrowException_WhenBrokerServiceThrows() {
        when(brokerService.getBrokerInfo(any(ComContext.class))).thenThrow(new AutoQuoteException("error"));

        assertThrows(AutoQuoteException.class, () ->
                distributorFacade.retrieveBrokerInfo(apiKey, language, province, postalCode, subBrokerNo, origin)
        );
        verify(brokerService, times(1)).getBrokerInfo(any(ComContext.class));
    }
}
