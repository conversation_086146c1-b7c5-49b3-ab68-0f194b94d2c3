package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.DriverDTO;
import intact.lab.autoquote.backend.common.dto.PartyDTO;
import intact.lab.autoquote.backend.common.dto.QuoteDTO;
import intact.lab.autoquote.backend.common.enums.PartyTypeEnum;
import intact.lab.autoquote.backend.common.enums.ProvinceEnum;
import intact.lab.autoquote.backend.validation.rule.DriverLicenseTypeValidationRule;
import intact.lab.autoquote.backend.validation.rule.LicenseObtentionDateValidationRule;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class DriverDTOValidatorTest {

	@InjectMocks
	private DriverDTOValidator driverDtoValidator;

	@Mock
	private DriverLicenseTypeValidationRule driverLicenseTypeValidationRule;

	@Mock
	private LicenseObtentionDateValidationRule licenseObtentionDateValidationRule;

	private Errors errors;

	private DriverDTO driverDTO;

	private ValidationContext context;

	@BeforeEach
	public void setUp() {
		driverDTO = new DriverDTO();
		driverDTO.setDriverLicenseType("R");
		driverDTO.setLicenseObtentionDate(new LocalDate(1997, 1, 1));
		driverDTO.setId(99);
		errors = new BeanPropertyBindingResult(driverDTO, "driverDTO");
		context = new ValidationContext("ON", "EN", "ABC", null);
	}

	@Test
	public void testValidateId_driverIdIsNotNull_valueAccepted() {
		driverDTO.setId(99);
		driverDtoValidator.validate(driverDTO, errors, context);
		assertFalse(errors.hasErrors(), "Errors has errors should be false because driver id was provided");
	}

	@Test
	public void testValidateDriverLicenseType_provinceIsNotOntario_noValidationRequired() {
		List<String> provinces = Arrays.asList(ProvinceEnum.QUEBEC.getCode(), ProvinceEnum.ALBERTA.getCode());
		for (String province : provinces) {
			context = new ValidationContext(province, "EN", "ABC", null);
			driverDtoValidator.validate(driverDTO, errors, context);
			verify(driverLicenseTypeValidationRule, times(0)).validate(anyString(), anyString(), anyString(), any(Errors.class), anyString());
		}
	}

	@Test
	public void testValidateDriverLicenseType_provinceIsOntario_validationIsRequired() {
		context = new ValidationContext(ProvinceEnum.ONTARIO.getCode(), "EN", "ABC", null);
		driverDtoValidator.validateDriverLicenseType(driverDTO.getDriverLicenseType(), errors, context);
		verify(driverLicenseTypeValidationRule, times(1)).validate(anyString(), anyString(), anyString(), any(Errors.class), anyString());
	}

	@Test
	public void testValidateLicenseObtentionDate_provinceIsNotONorAB_noValidationRequired() {
		List<String> provinces = Collections.singletonList(ProvinceEnum.QUEBEC.getCode());
		for (String province : provinces) {
			context = new ValidationContext(province, "EN", "ABC", null);
			driverDtoValidator.validate(driverDTO, errors, context);
			verify(licenseObtentionDateValidationRule, times(0)).validate(any(LocalDate.class), any(PartyDTO.class), anyString(), anyString(), any(Errors.class), anyString());
		}
	}

	@Test
	public void testValidateLicenseObtentionDate_provinceIsONorAB_validationRequired() {
		context = new ValidationContext(ProvinceEnum.ONTARIO.getCode(), "EN", "ABC", null);
		driverDtoValidator.validateLicenseObtentionDate(driverDTO.getLicenseObtentionDate(), errors, context);
		verify(licenseObtentionDateValidationRule, times(1)).validate(any(LocalDate.class), any(), anyString(), anyString(), any(Errors.class), anyString());

		context = new ValidationContext(ProvinceEnum.ALBERTA.getCode(), "EN", "ABC", null);
		driverDtoValidator.validateLicenseObtentionDate(driverDTO.getLicenseObtentionDate(), errors, context);
		verify(licenseObtentionDateValidationRule, times(2)).validate(any(LocalDate.class), any(), anyString(), anyString(), any(Errors.class), anyString());
	}

	@Test
	public void testGetPartyByType_quoteDTOisNull_returnNull() {
		driverDTO.setLicenseObtentionDate(new LocalDate());
		context = new ValidationContext("ON", "EN", "XXX", null);
		driverDtoValidator.validateLicenseObtentionDate(driverDTO.getLicenseObtentionDate(), errors, context);
		verify(licenseObtentionDateValidationRule, times(1)).validate(driverDTO.getLicenseObtentionDate(), null, context.getProvince(), context.getLanguage(), errors, "licenseObtentionDate");
	}

	@Test
	public void testGetPartyByType_quoteDTONotNull_returnPartyType() {
		PartyDTO partyPerson = new PartyDTO();
		partyPerson.setPartyType(PartyTypeEnum.PERSON);

		PartyDTO partyDriver = new PartyDTO();
		partyDriver.setPartyType(PartyTypeEnum.DRIVER);

		PartyDTO partyBusiness = new PartyDTO();
		partyBusiness.setPartyType(PartyTypeEnum.COMPANY);

		QuoteDTO quoteDto = new QuoteDTO();
		quoteDto.setParties(Arrays.asList(partyPerson, partyDriver, partyBusiness));

		driverDTO.setLicenseObtentionDate(new LocalDate());
		context = new ValidationContext("ON", "EN", "XXX", quoteDto);

		driverDtoValidator.validateLicenseObtentionDate(driverDTO.getLicenseObtentionDate(), errors, context);
		verify(licenseObtentionDateValidationRule, times(1)).validate(driverDTO.getLicenseObtentionDate(), partyPerson, context.getProvince(), context.getLanguage(), errors, "licenseObtentionDate");
	}
}
