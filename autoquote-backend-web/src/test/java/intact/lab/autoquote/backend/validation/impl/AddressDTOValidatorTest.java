package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.dto.AddressDTO;
import intact.lab.autoquote.backend.common.exception.BRulesExceptionEnum;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.Errors;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Test for the {@link AddressDTOValidator} class
 */
@ExtendWith(MockitoExtension.class)
public class AddressDTOValidatorTest {

	// Class under test
	private AddressDTOValidator addressDTOValidator;

	private AddressDTO addressDTO;

	private Errors errors;

	private ValidationContext context;

	private List<String> provinces;

	/**
	 * Private method used to validate the errors rejected by the test class
	 *
	 * @param errors         {@link Errors}
	 * @param errorField     The field on which the error should be
	 * @param BRuleExpection The error code for the expected error
	 * @param triggerValue   The String that was sent to trigger the error
	 */
	private static void assertHasError(Errors errors, String errorField, String BRuleExpection, String triggerValue) {
		assertTrue(errors.hasErrors(), String.format("Errors hasErrors should be true because %s [ \"%s\" ] is not valid", errorField, triggerValue));
		assertNotNull(errors.getFieldError(errorField), String.format("%s error", errorField));
		assertEquals(BRuleExpection, errors.getAllErrors().getFirst().getCode(), String.format("Error code should be %s", BRuleExpection));
	}

	@BeforeEach
	public void setUp() {
		addressDTOValidator = new AddressDTOValidator();
		provinces = Arrays.asList("ON", "QC", "AB");
		addressDTO = new AddressDTO();
		addressDTO.setPostalCode("T2A2A2");
		errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");
		context = new ValidationContext("QC", "FR", "intact", null);
	}

	@AfterEach
	public void tearDown() {
		addressDTOValidator = null;
		provinces = null;
		context = null;
		addressDTO = null;
		errors = null;
	}

	@Test
	public void testValidateProvince_AddressProvinceABNotEqualsContextProvinceQC_rejectedProvince() {
		// Given
		final String province = "AB";
		addressDTO.setProvince(province);
		context = new ValidationContext("QC", "FR", "intact", null);

		// When
		addressDTOValidator.validate(addressDTO, errors, context);

		// Then
		String errorField = "province";
		String bRuleException = BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode();

		assertHasError(this.errors, errorField, bRuleException, province);
	}

	@Test
	public void testValidateProvince_AddressProvinceONNotEqualsContextProvinceQC_rejectedProvince() {
		// Given
		final String province = "ON";
		addressDTO.setProvince(province);
		context = new ValidationContext("QC", "FR", "intact", null);

		// When
		addressDTOValidator.validate(addressDTO, errors, context);

		// Then
		String errorField = "province";
		String bRuleException = BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode();

		assertHasError(this.errors, errorField, bRuleException, province);
	}

	@Test
	public void testValidateProvince_HappyPath_valueAccepted() {
		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			context = new ValidationContext(province, "FR", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			assertFalse(errors.hasErrors(), String.format("Province %s should be accepted", province));
		}
	}

	@Test
	public void testValidateProvince_addressProvinceIsNull_rejectedProvince() {
		// Given
		addressDTO.setProvince(null);
		context = new ValidationContext("XX", "XX", "XX", null);

		// When
		addressDTOValidator.validate(addressDTO, errors, context);

		// Then
		assertTrue(errors.hasErrors(), "A null province should be rejected");
	}

	@Test
	public void testValidateProvince_addressProvinceIsEmpty_rejectedProvince() {
		// Given
		addressDTO.setProvince("");
		context = new ValidationContext("", "FR", "Intact", null);

		// When
		addressDTOValidator.validate(addressDTO, errors, context);

		// Then
		String errorField = "province";
		String bRuleException = BRulesExceptionEnum.ERR_VALUE_DOMAINE.getErrorCode();

		assertHasError(this.errors, errorField, bRuleException, "");
	}

	@Test
	public void testValidatePostalCode_postalCodeIsNull_rejectedNotBlank() {
		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			addressDTO.setPostalCode(null);
			context = new ValidationContext(province, "FR", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			String errorField = "postalCode";
			String BRuleExpection = BRulesExceptionEnum.NotBlank.getErrorCode();

			assertHasError(errors, errorField, BRuleExpection, "null");
		}
	}

	@Test
	public void testValidatePostalCode_postalCodeIsEmpty_rejectedBR768() {
		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			addressDTO.setPostalCode("");
			context = new ValidationContext(province, "EN", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			String errorField = "postalCode";
			String BRuleExpection = BRulesExceptionEnum.ERR_DRIVER_POSTALCODE_BR768.getErrorCode();

			assertHasError(errors, errorField, BRuleExpection, "");
		}
	}

	@Test
	public void testValidatePostalCode_postalCodeIsTooShort_rejectedBR768() {
		final String shortPostalCode = "12345";

		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			addressDTO.setPostalCode(shortPostalCode);
			context = new ValidationContext(province, "FR", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			String errorField = "postalCode";
			String BRuleExpection = BRulesExceptionEnum.ERR_DRIVER_POSTALCODE_BR768.getErrorCode();

			assertHasError(errors, errorField, BRuleExpection, shortPostalCode);
		}
	}

	@Test
	public void testValidatePostalCode_postalCodeIsTooLong_rejectedBR768() {
		final String postalCode = "1234567";

		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			addressDTO.setPostalCode(postalCode);
			context = new ValidationContext(province, "EN", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			String errorField = "postalCode";
			String BRuleExpection = BRulesExceptionEnum.ERR_DRIVER_POSTALCODE_BR768.getErrorCode();

			assertHasError(errors, errorField, BRuleExpection, postalCode);
		}
	}

	@Test
	public void testValidatePostalCode_postalCodeTrim_accepted() {
		final String postalCode = "   H 2 T   4 R 6    ";

		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			addressDTO.setPostalCode(postalCode);
			context = new ValidationContext(province, "FR", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			assertFalse(errors.hasErrors(), String.format("Postal code %s should be accepted (empty spaces should be trimmed)", postalCode));
		}
	}

	@Test
	public void testValidatePostalCode_postalCodeReplaceInvalidChar_accepted() {
		final String postalCode = " _ H . 2 . R , 2 - T / 9 _ ";

		for (String province : provinces) {
			// Given
			addressDTO.setProvince(province);
			addressDTO.setPostalCode(postalCode);
			context = new ValidationContext(province, "FR", "intact", null);
			errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

			// When
			addressDTOValidator.validate(addressDTO, errors, context);

			// Then
			assertFalse(errors.hasErrors(), String.format("Postal code %s should be accepted (invalid characters are removed)", postalCode));
		}
	}

	@Test
	public void testValidatePostalCode_postalCodeRegex_rejectedBR768() {
		List<String> invalidPostalCodes = Arrays.asList("2H2 2A2", "2h2 2A2", "H22 2A2", "h22 2a2", "HHH 2A2", "H2A XXX", "XXX XXX");

		for (String province : provinces) {
			for (String invalidPostalCode : invalidPostalCodes) {
				// Given
				addressDTO.setProvince(province);
				addressDTO.setPostalCode(invalidPostalCode);
				context = new ValidationContext(province, "FR", "intact", null);
				errors = new BeanPropertyBindingResult(addressDTO, "quoteDTO");

				// When
				addressDTOValidator.validate(addressDTO, errors, context);

				// Then
				String errorField = "postalCode";
				String BRuleExpection = BRulesExceptionEnum.Pattern.getErrorCode();

				assertHasError(errors, errorField, BRuleExpection, invalidPostalCode);
			}
		}
	}
}
