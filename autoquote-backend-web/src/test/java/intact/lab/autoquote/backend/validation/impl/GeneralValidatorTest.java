package intact.lab.autoquote.backend.validation.impl;

import intact.lab.autoquote.backend.common.exception.AutoQuoteApiParametersException;
import org.junit.jupiter.api.Test;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class GeneralValidatorTest {

    String INVALID_PROVINCE_MESSAGE = "The province passed as a parameter is null. Please, provide de non null parameter";
    String INVALID_LANGUAGE_MESSAGE = "The language passed as a parameter is null. Please, provide de non null parameter";

    @Test
    void ValidateApiParameter_Should_ThrowsLanguageNullError_When_LanguageIsNull() {

        AutoQuoteApiParametersException exception = assertThrows(AutoQuoteApiParametersException.class,
                () -> GeneralValidator.validateApiParameter(null, "ON"));
        assertEquals(INVALID_LANGUAGE_MESSAGE, exception.getMessage());
    }

    @Test
    void ValidateApiParameter_Should_ThrowsProvinceNullError_When_ProvinceIsNull() {

        AutoQuoteApiParametersException exception = assertThrows(AutoQuoteApiParametersException.class,
                () -> GeneralValidator.validateApiParameter("en", null));
        assertEquals(INVALID_PROVINCE_MESSAGE, exception.getMessage());
    }

    @Test
    void ValidateApiParameter_Should_ThrowsLanguageNullError_When_LanguageIsEmpty() {

        AutoQuoteApiParametersException exception = assertThrows(AutoQuoteApiParametersException.class,
                () -> GeneralValidator.validateApiParameter("", "ON"));
        assertEquals(INVALID_LANGUAGE_MESSAGE, exception.getMessage());
    }

    @Test
    void ValidateApiParameter_Should_ThrowsProvinceNullError_When_ProvinceIsEmpty() {

        AutoQuoteApiParametersException exception = assertThrows(AutoQuoteApiParametersException.class,
                () -> GeneralValidator.validateApiParameter("en", ""));
        assertEquals(INVALID_PROVINCE_MESSAGE, exception.getMessage());
    }
}
