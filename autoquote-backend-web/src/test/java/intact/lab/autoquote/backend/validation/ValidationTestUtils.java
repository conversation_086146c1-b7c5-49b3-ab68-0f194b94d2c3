package intact.lab.autoquote.backend.validation;


import org.springframework.validation.Errors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

public class ValidationTestUtils {

	public static void assertHasError(Errors errors, String errorField, String bRuleException) {
		assertTrue(errors.hasErrors());
		assertNotNull(errors.getFieldError(errorField));
		assertEquals(bRuleException, errors.getAllErrors().getFirst().getCode());
	}

	public static void assertNoErrors(Errors errors) {
		assertFalse(errors.hasErrors());
	}
}
