package intact.lab.autoquote.backend.common.data;

public interface DataConstant {
    String EXCEPTION_MESSAGE = "An exception occured !";
    String EXPECTED_MESSAGE = "Hello, <PERSON>!";
    String ILLEGAL_GREETING_VALUE_EXCEPTION_MESSAGE = "Looks like your greeting skills need a reboot!";
    String METHOD_NOT_ALLOWED_MESSAGE = "Post Method Not Allowed !";
    String USER_NAME = "Alan";
    String USER_NAME_FINISH_WITH_NUMBER = "Alan5";
    String USER_NAME_FINISH_WITH_SPACE = "Alan ";
    String USER_NAME_FINISH_WITH_WRONG_CHARACTER = "Alan%";
    String USER_NAME_START_WITH_NUMBER = "4Alan";
    String USER_NAME_START_WITH_SPACE = " Alan";
    String USER_NAME_START_WITH_WRONG_CHARACTER = "%Alan";
    String USER_NAME_WITH_NUMBER = "Al6an";
    String USER_NAME_WITH_SPACE = "Al an";
    String USER_NAME_WITH_WRONG_CHARACTER = "Al%an";
    long GREETING_ID = 1l;
}
