package intact.lab.autoquote.backend.converter.impl;

import com.intact.com.enums.ComCoverageAmountTypeEnum;
import com.intact.com.offer.ComCoverageItem;
import com.intact.com.offer.ComItemChoice;
import com.intact.com.offer.enums.ComCoverageNameEnum;
import intact.lab.autoquote.backend.common.dto.CoverageDTO;
import intact.lab.autoquote.backend.common.dto.CoverageValueDTO;
import intact.lab.autoquote.backend.common.enums.CoverageValueType;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class COMCoverageConverterTest {

	@InjectMocks
	private COMCoverageConverter converter;

	@Mock
	private COMCoverageValueConverter mockCoverageValueConverter;

	@Test
	public void toCOM() {
		assertThrows(UnsupportedOperationException.class, () -> {
			this.converter.toCOM(null, null);
		});
	}

	@Test
	public void toDTO() throws Exception {
		ComCoverageItem comCoverageItem = new ComCoverageItem();
		comCoverageItem.setCoverageIhvCode("AL30");
		comCoverageItem.setCoverageInternalCode("LIAB");
		comCoverageItem.setCoverageName(ComCoverageNameEnum.LIABILITY);
		comCoverageItem.setCoverageValue("2000000");
		comCoverageItem.setSelectable(false);
		comCoverageItem.setEligible(true);

		ComItemChoice comItemChoice = new ComItemChoice();
		comItemChoice.setCoverageAmountType(ComCoverageAmountTypeEnum.LIMIT_AMOUNT);
		comCoverageItem.setChoices(new ComItemChoice[]{comItemChoice});

		CoverageValueDTO mockCoverageValueDTO = new CoverageValueDTO();
		mockCoverageValueDTO.setAmount(2000000);
		mockCoverageValueDTO.setSelectedInd(true);
		mockCoverageValueDTO.setCoverageValueType(CoverageValueType.LIMIT_AMOUNT);

		when(this.mockCoverageValueConverter.toDTO(any(ComItemChoice.class))).thenReturn(mockCoverageValueDTO);

		CoverageDTO coverageDTO = this.converter.toDTO(comCoverageItem);

		assertEquals("AL30", coverageDTO.getIhvCode());
		assertEquals("LIAB", coverageDTO.getCode());
		assertEquals(false, coverageDTO.getSelectableInd());
		assertEquals(true, coverageDTO.getSelectedInd());
		assertEquals(true, coverageDTO.getCoverageValues().getFirst().getSelectedInd());
	}

}
