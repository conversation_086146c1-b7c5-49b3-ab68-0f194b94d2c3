package intact.lab.autoquote.backend.quotestatemanager.impl;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.intact.business.rules.offer.BR263_1_MonthlyPremiumAvailable;
import intact.lab.autoquote.backend.facade.impl.FacadeTestUtil;
import intact.lab.autoquote.backend.services.business.driver.IDriverBusinessProcess;
import intact.lab.autoquote.backend.services.business.vehicle.IVehicleBusinessProcess;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;

import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.driver.BR449_LimitDrivers;
import com.intact.business.rules.vehicle.BR1797_LimitTwoVehicle;
import com.intact.com.state.ComState;

class QuoteStateManagerIntactTest extends FacadeTestUtil {

    /** Class to be tested. Since the class is abstract, it is tested using the ON implementation. */
    @InjectMocks
    private QuoteStateManagerONIntactCL stateManagerFacade;

    /** Test objects **/
    private PolicyVersion currentTestPV;

    private ComState currentTestState;

    /** Mocks */
    @Mock
    private IDriverBusinessProcess mockDriverBusinessProcess;

    @Mock
    private IVehicleBusinessProcess mockVehicleBusinessProcess;

    @Mock
    private BR263_1_MonthlyPremiumAvailable BR263;

    private static MockedStatic<BR1797_LimitTwoVehicle> mockedBR1797;
    private static MockedStatic<BR449_LimitDrivers> mockedBR449;

    /** Constants */
    private static final Integer MAX_FOR_TEST = 5;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        // PolicyVersion and ComState setup
        this.currentTestPV = new PolicyVersion();
        this.currentTestState = new ComState();
    }

    @BeforeAll
    public static void initMocks() {
        mockedBR1797 = Mockito.mockStatic(BR1797_LimitTwoVehicle.class);
        mockedBR449 = Mockito.mockStatic(BR449_LimitDrivers.class);

        mockedBR1797.when(BR1797_LimitTwoVehicle::getMaxVehicle).thenReturn(MAX_FOR_TEST);
        mockedBR449.when(BR449_LimitDrivers::getMaxDrivers).thenReturn(MAX_FOR_TEST);
    }

    @AfterEach
    void tearDown() {
        this.currentTestPV = null;
        this.currentTestState = null;
    }

    @Test
    void testUpdateState() {
        // Executing the tested method and validating the results
        this.stateManagerFacade.updateState(this.currentTestState, this.currentTestPV);

        assertNull(this.currentTestState.getRoadblockInd(), "The Com State's roadblockInd attribute should've been set to null.");
        assertNull(this.currentTestState.getFirstRate(), "The Com State's firstRate attribute should've been set to null.");
        assertNull(this.currentTestState.getShowPreviousAddress(), "The Com State's showPreviousAddress attribute should've been set to null.");
        assertNull(this.currentTestState.getHasOffer(), "The Com State's hasOffer attribute should've been set to null.");

        assertFalse(this.currentTestState.isCanAddDriver(), "The Com State's canAddDriver attribute should've been set to false.");
        assertFalse(this.currentTestState.isCanRemoveDriver(), "The Com State's canRemoveDriver attribute should've been set to false.");
        assertFalse(this.currentTestState.isCanAddVehicle(), "The Com State's canAddVehicle attribute should've been set to false.");
        assertFalse(this.currentTestState.isCanRemoveVehicle(), "The Com State's canRemoveVehicle attribute should've been set to false.");
        assertFalse(this.currentTestState.isCanBind(), "The Com State's canBind attribute should've been set to false.");
        assertFalse(this.currentTestState.isCanShowUsage(), "The Com State's canShowUsage attribute should've been set to false.");
    }

    @Test
    void testCanRate_VehiclesAndDriversOverMax() {
        // Setting the vehicles and drivers to be over maximum
        when(this.mockVehicleBusinessProcess.getVehicleCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST + 1);
        when(this.mockDriverBusinessProcess.getDriversCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST + 1);

        // Executing the tested method and validating the results
        this.stateManagerFacade.updateState(this.currentTestState, this.currentTestPV);

        assertFalse(this.currentTestState.isCanRate(), "The Com State's canRate attribute should've been set to false");
    }

    @Test
    void testCanRate_VehiclesOverMax() {
        // Setting for only the vehicles to be over maximum
        when(this.mockVehicleBusinessProcess.getVehicleCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST + 1);
        when(this.mockDriverBusinessProcess.getDriversCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST - 1);

        // Executing the tested method and validating the results
        this.stateManagerFacade.updateState(this.currentTestState, this.currentTestPV);

        assertFalse(this.currentTestState.isCanRate(), "The Com State's canRate attribute should've been set to false");
    }

    @Test
    void testCanRate_DriversOverMax() {
        // Setting only the drivers to be over maximum
        when(this.mockVehicleBusinessProcess.getVehicleCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST - 1);
        when(this.mockDriverBusinessProcess.getDriversCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST + 1);

        // Executing the tested method and validating the results
        this.stateManagerFacade.updateState(this.currentTestState, this.currentTestPV);

        assertFalse(this.currentTestState.isCanRate(), "The Com State's canRate attribute should've been set to false");
    }

    @Test
    void testCanRate_DriversAndVehiclesAtMax() {
        // Setting the vehicles and drivers to be at maximum
        when(this.mockVehicleBusinessProcess.getVehicleCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST);
        when(this.mockDriverBusinessProcess.getDriversCount(any(PolicyVersion.class))).thenReturn(MAX_FOR_TEST);

        // Executing the tested method and validating the results
        this.stateManagerFacade.updateState(this.currentTestState, this.currentTestPV);

        assertTrue(this.currentTestState.isCanRate(), "The Com State's canRate attribute should've been set to true");
    }
}
