package com.intact.globaladmin.domain.enums;

import com.intact.bod.basicdatatypes.datatype.CodeValue;

public enum Province{
    NL,
    NS,
    PE,
    NB,
    QC,
    ON,
    MB,
    SK,
    AB,
    BC,
    YT,
    NT,
    NU;

    public String toString(){
        return name();
    }
    
    public CodeValue toCodeValue(){
        return new CodeValue( name() );
    }
    
    public static Province convert(final CodeValue province ){
        if(province == null){
            throw new IllegalArgumentException( "Province must not be null" );
        }

        return valueOfCode(province.getCodeValue() );
    }
    
    public static Province valueOfCode(final String provinceAsString ){
        for(final Province province : values() ){
            if(province.name().equalsIgnoreCase(provinceAsString) ){
                return province;
            }
        }

        throw new IllegalArgumentException( "Province '" + provinceAsString + "' is not valid" );
    }
}