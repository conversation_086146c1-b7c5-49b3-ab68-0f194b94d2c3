package com.intact.globaladmin.domain.enums;

import com.intact.bod.basicdatatypes.datatype.CodeValue;

public enum ReferenceUuIdEnum {//Rename

	CONT;

	public String toString() {
		return name();
	}

	public CodeValue toCodeValue() {
		return new CodeValue(name());
	}

	public static ReferenceUuIdEnum convert(final CodeValue referenceUuId) {
		if (referenceUuId == null) {
			throw new IllegalArgumentException("ReferenceUuId must not be null");
		}

		return valueOfCode(referenceUuId.getCodeValue());
	}

	public static ReferenceUuIdEnum valueOfCode(final String ReferenceUuIdAsString) {
		for (final ReferenceUuIdEnum referenceUuId : values()) {
			if (referenceUuId.name().equalsIgnoreCase(ReferenceUuIdAsString)) {
				return referenceUuId;
			}
		}

		throw new IllegalArgumentException("ReferenceUuId '" + ReferenceUuIdAsString + "' is not valid");
	}

}
