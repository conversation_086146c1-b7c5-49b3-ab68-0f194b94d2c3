package com.intact.globaladmin.service.util;

import java.util.List;

import com.intact.globaladmin.domain.enums.Classification;
import com.intact.globaladmin.domain.enums.MimeType;
import com.intact.globaladmin.domain.enums.RequestStatus;
import com.intact.globaladmin.domain.enums.UnderwritingCompanyEnum;

public class DriverLicenceDocumentSearch {

	private String drivingLicense = null;
    private String referenceFolder = null;
	private UnderwritingCompanyEnum company = null;
	private MimeType mimeType = null;
	private Classification classification = null;
	private Long ageOfValidReport = null;
	private List<RequestStatus> requestStatuses = null;
	private String distributorNumber = null;

	public DriverLicenceDocumentSearch() {

	}

	public String getDrivingLicense() {
		return this.drivingLicense;
	}

	public void setDrivingLicense(String drivingLicense) {
		this.drivingLicense = drivingLicense;
	}

    public String getReferenceFolder() {
        return this.referenceFolder;
    }

    public void setReferenceFolder(String referenceFolder) {
        this.referenceFolder = referenceFolder;
    }

	public UnderwritingCompanyEnum getCompany() {
		return this.company;
	}

	public void setCompany(UnderwritingCompanyEnum company) {
		this.company = company;
	}

	public MimeType getMimeType() {
		return this.mimeType;
	}

	public void setMimeType(MimeType mimeType) {
		this.mimeType = mimeType;
	}

	public Classification getClassification() {
		return this.classification;
	}

	public void setClassification(Classification classification) {
		this.classification = classification;
	}

	public Long getAgeOfValidReport() {
		return this.ageOfValidReport;
	}

	public void setAgeOfValidReport(Long ageOfValidReport) {
		this.ageOfValidReport = ageOfValidReport;
	}

	public List<RequestStatus> getRequestStatuses() {
		return this.requestStatuses;
	}

	public void setRequestStatuses(List<RequestStatus> requestStatuses) {
		this.requestStatuses = requestStatuses;
	}

	public String getDistributorNumber() {
		return this.distributorNumber;
	}

	public void setDistributorNumber(String distributorNumber) {
		this.distributorNumber = distributorNumber;
	}

	@Override
	public String toString() {
		return "driving license=" + getDrivingLicense() + ", company=" + getCompany() + ", mime type=" + getMimeType() + ", classification=" + getClassification()
				+ ", age of valid report=" + getAgeOfValidReport() + ", request statuses=" + getRequestStatuses() + ", distributor number=" + getDistributorNumber() + "]";
	}

}
