/**
 * 
 */
package com.intact.globaladmin.dao;

import java.util.List;

import com.intact.globaladmin.domain.EventCategory;
import com.intact.globaladmin.domain.enums.EventCategoryCodeEnum;
import com.intact.globaladmin.domain.enums.EventNatureCodeEnum;

/**
 * <AUTHOR>
 *
 */
public interface IEventCategoryDAO {

	List<EventCategory> findListEventsByContractIdAndCategoryCode(String contractId, EventCategoryCodeEnum categoryCode, boolean endTimeNotNull);
	
	EventCategory findLatestEventCategoryByContractIdAndCategoryCode(String contractId, EventCategoryCodeEnum categoryCode);

	List<EventCategory> findListEventsCategoryByContractIdAndNatureCode(String contractId, EventNatureCodeEnum natureCode);
}
