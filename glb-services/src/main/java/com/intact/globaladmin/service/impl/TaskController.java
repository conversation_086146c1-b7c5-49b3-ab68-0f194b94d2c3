/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2013, Intact Insurance, All rights reserved.<br>
 */
package com.intact.globaladmin.service.impl;

import java.util.Set;

import jakarta.inject.Named;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.intact.globaladmin.dao.ITaskDAO;
import com.intact.globaladmin.domain.Event;
import com.intact.globaladmin.domain.Task;
import com.intact.globaladmin.exception.TaskServiceException;
import com.intact.globaladmin.service.ITaskController;

/**
 * The Class TaskController.
 */
@Named
public class TaskController implements ITaskController {

	private static final Log logger = LogFactory.getLog(TaskController.class);

	private static final String NO_TASK_FOUND_MSG = "No Task found in database where contract id = '%s' !";

	private static final String INVALID_CONTRACT_ID_MSG = "Contract id %s is null or empty!";

	private static final String INVALID_LIST_EVENTS_MSG = "Events list %s is null or empty!";

	@Autowired
	private ITaskDAO taskDAO;
	
	@Override
	public Task createTask(final Task task) {
		taskDAO.createTask(task);
		return task;
	}

	@Override
	public Task updateTaskEvents(String referenceUuId, Set<Event> events) throws TaskServiceException {

		try {
			validateContractIdIsNullOrEmpty(referenceUuId);
			validateEventsListIsNullOrEmpty(events);
			Task currentTask = findTaskByContractId(referenceUuId);
			if (currentTask == null) {
				String errorMsg = String.format(NO_TASK_FOUND_MSG, referenceUuId);
				throw new TaskServiceException(errorMsg);
			}
			currentTask.setEvents(events);
			taskDAO.updateTaskEvents(currentTask);
			return currentTask;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			throw new TaskServiceException(e);
		}
	}

	@Override
	public Task findTaskByContractId(String referenceUuId) {
		Task task = taskDAO.findTaskByContractId(referenceUuId);
		return task;
	}
	
	public ITaskDAO getTaskDAO() {
		return taskDAO;
	}

	public void setTaskDAO(ITaskDAO taskDAO) {
		this.taskDAO = taskDAO;
	}

	/***/
	private void validateContractIdIsNullOrEmpty(String referenceUuId) throws TaskServiceException {
		if (StringUtils.isEmpty(referenceUuId)) {
			throw new TaskServiceException(String.format(INVALID_CONTRACT_ID_MSG, referenceUuId));
		}
	}

	/***/
	private void validateEventsListIsNullOrEmpty(Set<Event> events) throws TaskServiceException {
		if (CollectionUtils.isEmpty(events)) {
			throw new TaskServiceException(String.format(INVALID_LIST_EVENTS_MSG, events));
		}
	}

	@Override
	public Task cloneTaskByContractId(Task task) {
		Task clone = this.createTask(task);
		return clone;
	}
	@Override
	public void updateTask(Task task) {
		taskDAO.updateTaskEvents(task);
	}
	
	@Override
	public void saveTask(Task task) {
		 this.taskDAO.saveTask(task);
	}
	
}
