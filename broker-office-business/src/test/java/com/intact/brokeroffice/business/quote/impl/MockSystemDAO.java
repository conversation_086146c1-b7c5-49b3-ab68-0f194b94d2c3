package com.intact.brokeroffice.business.quote.impl;

import com.intact.brokeroffice.dao.SystemDAO;

public class MockSystemDAO implements SystemDAO {
	
	private String uploadUser = null;
	private String uploadSystem = null;
	private String user = null;
	private String language = null;
	private String remoteURL = null;
	private String master = null;
	private String company = null;

	@Override
	public String getUploadUser() {
		// TODO Auto-generated method stub
		return this.uploadUser;
	}

	@Override
	public String getUploadSystem() {
		// TODO Auto-generated method stub
		return this.uploadSystem;
	}

	@Override
	public String getSourceUser() {
		// TODO Auto-generated method stub
		return this.user;
	}

	@Override
	public String getLanguage() {
		// TODO Auto-generated method stub
		return this.language;
	}

	@Override
	public String getRemoteSystemUrl() {
		return this.remoteURL;
	}

	@Override
	public String getMasterBroker() {
		return this.master;
	}

	@Override
	public String getCompany() {
		return this.company;
	}

	public String getUser() {
		return user;
	}

	public void setSourceUser(String user) {
		this.user = user;
	}

	public String getRemoteURL() {
		return remoteURL;
	}

	public void setRemoteURL(String remoteURL) {
		this.remoteURL = remoteURL;
	}

	public String getMaster() {
		return master;
	}

	public void setMaster(String master) {
		this.master = master;
	}

	public void setUploadUser(String uploadUser) {
		this.uploadUser = uploadUser;
	}

	public void setUploadSystem(String uploadSystem) {
		this.uploadSystem = uploadSystem;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public void setCompany(String company) {
		this.company = company;
	}

}
