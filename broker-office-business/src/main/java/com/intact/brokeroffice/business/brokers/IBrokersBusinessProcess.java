package com.intact.brokeroffice.business.brokers;

import java.util.List;
import java.util.Map;
import com.ing.canada.cif.domain.IBrokersInfos;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;


public interface IBrokersBusinessProcess {	
	
	/**
	 * Gets the list of broker infos from company number
	 * @param companyNumber
	 * @return the list of broker infos
	 */
	List<IBrokersInfos> getBrokersInfos(String companyNumber);

	/**
	 * Get broker info from sub broker number
	 * @param subBrokerNo
	 * @return the broker infos 
	 */
	IBrokersInfos getBrokerInfos(String subBrokerNo);	
	
	/**
	 * Gets the list of master brokers.
	 * @param aCifCompanyEnum
	 * @param apps the list of application ids
	 * @param lobs the list of lines of business
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user.
 	 * 
	 * @return the list of master brokers
	 */
	Map<String,String> getAssignedMasterBrokers(CifCompanyEnum aCifCompanyEnum, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs);
	
	/**
	 * Gets the list of master brokers.
	 * @param subBrokerNbrList
	 * @param adminRole
	 * @param aCifCompanyEnum
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user.
 	 * 
	 * @return the list of master brokers
	 */
	Map<String,String> getAssignedMasterBrokers(List<String> subBrokerNbrList, boolean adminRole, CifCompanyEnum aCifCompanyEnum, String aManufacturerCompanyCode);
	

}
