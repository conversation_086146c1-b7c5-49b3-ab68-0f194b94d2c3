package com.intact.brokeroffice.business.domain;

import java.io.Serial;

import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;

public class BrokerQuoteSearchCriteria extends QuoteSearchCriteria {

	@Serial
	private static final long serialVersionUID = -6625754151383007990L;

	private String quoteSource;

	private String[] selectedLinesOfBusiness;

	private String[] selectedLinesOfInsurance;

	public BrokerQuoteSearchCriteria() {
		super();
	}

	public String getQuoteSource() {
		return quoteSource;
	}

	public void setQuoteSource(String quoteSource) {
		this.quoteSource = quoteSource;
	}

	public String[] getSelectedLinesOfBusiness() {
		return selectedLinesOfBusiness;
	}

	public void setSelectedLinesOfBusiness(String[] selectedLinesOfBusiness) {
		this.selectedLinesOfBusiness = selectedLinesOfBusiness;
	}
	
	public String[] getSelectedLinesOfInsurance() {
		return selectedLinesOfInsurance;
	}
	
	public void setSelectedLinesOfInsurance(String[] selectedLinesOfInsurance) {
		this.selectedLinesOfInsurance = selectedLinesOfInsurance;
	}
}
	
