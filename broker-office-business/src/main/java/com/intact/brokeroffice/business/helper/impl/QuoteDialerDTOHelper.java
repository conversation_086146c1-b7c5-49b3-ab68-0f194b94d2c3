package com.intact.brokeroffice.business.helper.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Named;

import org.apache.commons.lang3.StringUtils;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.util.CollectionUtils;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.intact.brokeroffice.business.domain.dialer.PartyDTO;
import com.intact.brokeroffice.business.domain.dialer.PhoneDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.DatasourceOriginEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.LineofBusinessEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.QuoteAppEnumEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.QuoteSourceEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.SourceUnderwritingCompanyEnum;
import com.intact.brokeroffice.business.domain.dialer.VehicleDTO;
import com.intact.brokeroffice.business.helper.IQuoteDialerDTOHelper;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.plt.information.service.client.util.InformationPieceTO;

/**
 * Builder class for the QuoteDialerDTO object and its sub objects (VehicleDTO and PartyDTO).
 * <AUTHOR>
 *
 */
@Named
public class QuoteDialerDTOHelper implements IQuoteDialerDTOHelper {
	
	private static final Logger LOG = ESAPI.getLogger(QuoteDialerDTOHelper.class);
	
	/**
	 * Function used to build a QuoteDialerDTO object from the received parameters without initializing any of the sub classes (VehicleDTO and PartyDTO).
	 * @param quote The general QuotesBean object. 
	 * @param userContext The context information of the logged in user
	 * @param brokerNumber The broker number for the current quote
	 * @param applicationMode The type of the current quote (QF, QA, QH, PC, etc.)
	 * @param generalInfoPiece InformationPieceTO containing the general quote detail information
	 * @return QuoteDialerDTO The built QuoteDialerDTO object
	 */
	@Override
	public QuoteDialerDTO buildQuoteDialerDTO(QuotesBean quote, IUserContext userContext, String brokerNumber, String applicationMode, InformationPieceTO generalInfoPiece) {
		if (quote == null || userContext == null || generalInfoPiece == null) {
			LOG.info(Logger.EVENT_FAILURE, """
					Cannot build QuoteDialerDTO object - Null objects received.\s
					quote : %s; userContext: %s, generalInfoPiece : %s;\
					""".formatted(quote, userContext, generalInfoPiece)); 
			return null;
		}
		
		QuoteDialerDTO quoteDialerInformation = new QuoteDialerDTO();


		
		/*
		 *  Build the QuoteDialerDTO object to send to the dialer service
		 */
		
		// Set the dates in the QuoteDialerDTO. Reminder : QuoteDialerDTO in WebZone must have String dates.
		if (LineOfInsuranceCodeEnum.AUTOMOBILE.equals(quote.getLineOfInsurance())) {
			quoteDialerInformation.setInceptionDate(getInceptionDate(quote));
		}

    String pattern = "yyyy-MM-dd";
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
		quoteDialerInformation.setCreationTimeStamp(quote.getCreationDate() != null ? simpleDateFormat.format(quote.getCreationDate()) : null);
		
		quoteDialerInformation.setDistributorNumber(brokerNumber);
		
		if (quote.getLineOfBusiness() != null) {
			quoteDialerInformation.setLineofBusiness(StringUtils.equalsIgnoreCase(quote.getLineOfBusiness().getCode(), "P") ? LineofBusinessEnum.PERSONAL : LineofBusinessEnum.COMMERCIAL);
		}
		
		quoteDialerInformation.setQuoteNumber(quote.getAgreementNumber());
		
		if (quote.getQuoteSource() != null) {
			quoteDialerInformation.setQuoteSource(QuoteSourceEnum.fromValue(quote.getQuoteSource()));
		}
		
		quoteDialerInformation.setUserName(userContext.getUser());
		
		if (!CollectionUtils.isEmpty(quote.getChildrens())) {
			List<String> relatedQuotes = new ArrayList<String>();

			for (QuotesBean childQuote : quote.getChildrens())	{
				relatedQuotes.add(childQuote.getAgreementNumber());
			}
			
			quoteDialerInformation.setListRelatedQuotes(relatedQuotes);
		}

    QuoteAppEnumEnum dialerQuoteApp = this.convertApplicationModeToQuoteAppEnum(applicationMode);
    quoteDialerInformation.setQuoteAppEnum(dialerQuoteApp);

		// Ideally : change this when possible, as the datasource origin isn't the right way to check the type of quote (home, auto, etc.)
		DatasourceOriginEnum datasourceOrigin = this.convertQuoteAppEnumToDatasourceOriginEnum(dialerQuoteApp);
		quoteDialerInformation.setDatasourceOrigin(datasourceOrigin);
		
		SourceUnderwritingCompanyEnum dialerSourceUnderwritingCompany = this.convertCompanyToSourceUnderwritingCompanyEnum(userContext.getCompany());
		quoteDialerInformation.setSourceUnderwritingCompany(dialerSourceUnderwritingCompany);
		
		quoteDialerInformation.setPhone(this.obtainClientPhoneNumber(generalInfoPiece));
		

		
		return quoteDialerInformation;
	}

	private String getInceptionDate(QuotesBean quote) {
    // Set the dates in the QuoteDialerDTO. Reminder : QuoteDialerDTO in WebZone must have String dates.

    String pattern = "yyyy-MM-dd";
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);

    return quote.getInceptionDate()!= null ? simpleDateFormat.format(quote.getInceptionDate()) :
        quote.getCreationDate() !=null ? simpleDateFormat.format(quote.getCreationDate()) : "";
  }
	
	/**
	 * Function used to build a PartyDTO object from the received informations.
	 * @param driverInfoPiece The InformationPieceTO containing the driver information (for IRCA)
	 * @param generalInfoPiece The InformationPieceTO containing the principal owner name (for P&C)
	 * @param quote The general QuotesBean object. 
	 * @param policyHolderName The policy holder name (for Personal line)
	 * @param applicationMode The type of the current quote (QF, QA, QH, PC, etc.)
	 * @param provinceFromCompany The province obtained from the source underwriting company
	 * @return PartyDTO Built PartyDTO object
	 */
	@Override
	public PartyDTO buildDialerPartyDTO(InformationPieceTO driverInfoPiece, InformationPieceTO generalInfoPiece, QuotesBean quote, String policyHolderName,
			String applicationMode, String provinceFromCompany) {
		
		if (quote == null || (generalInfoPiece == null && driverInfoPiece == null && policyHolderName == null)) {
			LOG.info(Logger.EVENT_FAILURE, """
					Cannot build PartyDTO object - Null objects received.\s
					quote : %s; At least one of the following should have a value : -generalInfoPiece : %s;  -driverInfoPiece : %s; - policyHolderName : %s;\
					""".formatted(
					quote, generalInfoPiece, driverInfoPiece, policyHolderName)); 
			return null;
		}
		PartyDTO dialerParty = new PartyDTO();
		
		this.populateDialerPartyName(dialerParty, driverInfoPiece, generalInfoPiece, quote.getLineOfBusiness(), policyHolderName, applicationMode);
		
		if (StringUtils.isBlank(dialerParty.getFirstName()) && StringUtils.isBlank(dialerParty.getLastName())) {
			LOG.info(Logger.EVENT_FAILURE, "Cannot build PartyDTO object - Populated name is empty.".formatted()); 
			return null;
		}
		
		if (LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(quote.getLineOfBusiness()) && StringUtils.isNotBlank(quote.getUnstructuredName())) {
			dialerParty.setCompanyName(quote.getUnstructuredName());
		} else {
			dialerParty.setCompanyName(StringUtils.EMPTY);
		}
		
		// For P&C, try obtaining the province from the address field, otherwise set the received province (the one converted from the selected company)
		if (StringUtils.equalsIgnoreCase("PC", applicationMode)) { 
			provinceFromCompany = this.obtainProvinceFromAddressInsuredField(generalInfoPiece);
		} 
		dialerParty.setProvince(provinceFromCompany);
		
		return dialerParty;
	}
	
	/**
	 * Function used to build a list of VehicleDTO objects based on vehicle information and a selected language.
	 * @param vehicleInfoPiece The vehicle information string (containing at most a single vehicle's informations)
	 * @param language The language of the received information. Impacts the order of the information.
	 * @return List of VehicleDTO objects corresponding to the vehicles pieces and language received
	 */
	@Override
	public List<VehicleDTO> buildDialerVehicleDTOList(InformationPieceTO vehicleInfoPiece, String language) {
		
		if (vehicleInfoPiece == null || StringUtils.isBlank(language)) {
			LOG.info(Logger.EVENT_FAILURE, """
					Cannot build PartyDTO object - Null or empty objects received.\s
					vehicleInfoPiece : %s;\s
					Language : %s""".formatted(vehicleInfoPiece, language)); 
			return null;
		}
		
		List<VehicleDTO> vehicleList = new ArrayList<VehicleDTO>();			
		String vehicleInformation = this.obtainVehiclesInformation(vehicleInfoPiece);

		if (StringUtils.isNotBlank(vehicleInformation)) {
			VehicleDTO currentVehicle = null;
			String vehicleInfo = null;
			InformationPieceTO currentVehiclePiece = null;
			
			for (int vehicleIndex = 0; vehicleIndex < vehicleInfoPiece.getChildrens().size(); vehicleIndex++) {
				
				currentVehiclePiece = vehicleInfoPiece.getChildrens().get(vehicleIndex);
				
				if (currentVehiclePiece.getPieces() != null && currentVehiclePiece.getPieces().get(0) != null 
															&& StringUtils.isNotBlank(currentVehiclePiece.getPieces().get(0).getValue())) {
					vehicleInfo = currentVehiclePiece.getPieces().get(0).getValue();

					// Obtain the corresponding VehicleDTO object
					currentVehicle = this.convertVehicleInfoStringToVehicleDTO(vehicleInfo, language);
					if (currentVehicle != null) {
						vehicleList.add(currentVehicle);
					}
				}
			}
		}		
		
		return vehicleList;		
	}
	
	/**
	 * Function used to convert a vehicle information string (containing at most a single vehicle's informations) into
	 * the corresponding VehicleDTO object based on the received language.
	 * @param vehicleInformation The vehicle information string (containing at most a single vehicle's informations)
	 * @param language The language of the received information. Impacts the order of the information.
	 * @return VehicleDTO corresponding to the vehicle information and language received
	 */
	protected VehicleDTO convertVehicleInfoStringToVehicleDTO (String vehicleInformation, String language) {

		String[] vehicleSplitInfo = null;
		VehicleDTO currentVehicle = null;
		StringBuilder currentVehicleModel = new StringBuilder();
		
		if (StringUtils.isNotBlank(vehicleInformation) && StringUtils.isNotBlank(language)) {
			
			int startIndex = vehicleInformation.indexOf(" - ");
			int endIndex = vehicleInformation.indexOf("(");
			
			// If the vehicle info doesn't follow the template " - Vehicle infos (Car code:XXXXXXX)", return null
			if (startIndex >= 0 && endIndex >= 0) {
				
				// Remove unwanted parts of the vehicle note and split on spaces
				vehicleInformation = vehicleInformation.substring(startIndex + 3, endIndex);
				
				vehicleSplitInfo = vehicleInformation.split(" ");
				if (vehicleSplitInfo.length > 2) {			
					
					// In French, the information order is Make Model Year
					if(StringUtils.equalsIgnoreCase("FR", language)) {	
						
						currentVehicle = new VehicleDTO();
						currentVehicle.setMake(vehicleSplitInfo[0]);
						currentVehicle.setYear(vehicleSplitInfo[vehicleSplitInfo.length -1]);
						
						for (int j=1; j < vehicleSplitInfo.length - 1; j++) {
							currentVehicleModel = currentVehicleModel.append(" ").append(vehicleSplitInfo[j]);
						}
						currentVehicle.setModel(currentVehicleModel.toString().trim());
						
					} else if(StringUtils.equalsIgnoreCase("EN", language)) {
						
						// In English, the information order is Year Make Model		
						currentVehicle = new VehicleDTO();
						currentVehicle.setYear(vehicleSplitInfo[0]);
						currentVehicle.setMake(vehicleSplitInfo[1]);
						
						for (int j=2; j < vehicleSplitInfo.length; j++) {
							currentVehicleModel = currentVehicleModel.append(" ").append(vehicleSplitInfo[j]);
						}
						currentVehicle.setModel(currentVehicleModel.toString().trim());
					}
				}
			}
		}		
		return currentVehicle;
	}

	/**
	 * Function used to obtain the vehicle informations from an InformationPieceTO and returns the
	 * concatenated informations separated with a single space.
	 * @param vehicleInfoPiece The InformationPieceTO object containing the vehicles information
	 * @return String Concatenated vehicle informations separated with a single space
	 */
	protected String obtainVehiclesInformation(InformationPieceTO vehicleInfoPiece) {
		StringBuilder vehicleInfo = null;
		
		if (vehicleInfoPiece == null || vehicleInfoPiece.getChildrens().isEmpty()) {
			return null;
		}
		
		vehicleInfo = new StringBuilder();
		
		for (InformationPieceTO child: vehicleInfoPiece.getChildrens()) {
			
			if (child.getPieces().get(0) != null) {
				vehicleInfo.append(child.getPieces().get(0).getValue()).append(" ");
			}
		}

		return vehicleInfo.toString().trim();
	}
	
	/**
	 * Function used to populate the firstName and lastName fields of a PartyDTO object. 
	 * Name can come from different sources : 
	 * 	- Personal Lines : Policy holder name
	 *  - IRCA : First driver name
	 *  - P&C : Principal Owner name
	 * @param dialerParty The PartyDTO in which to populate the name elements
	 * @param driverInfoPiece InformationPieceTO containing the driver informations (for IRCA)
	 * @param generalInfoPiece InformationPieceTO containing the principal owner name (for P&C)
	 * @param lineOfBusiness The quote's LineOfBusinessCodeEnum value
	 * @param policyHolderName The policy holder name
	 * @param applicationMode The type of the current quote (QF, QA, QH, PC, etc.)
	 */
	protected void populateDialerPartyName(PartyDTO dialerParty, InformationPieceTO driverInfoPiece, InformationPieceTO generalInfoPiece,
											LineOfBusinessCodeEnum lineOfBusiness, String policyHolderName, String applicationMode) {		
		String firstName = "";
		String lastName = "";
		
		if (dialerParty != null) {
			
			// If Personal Lines, get the policy holder name. If IRCA, get the driver name. If P&C, get the principal owner name.
			if (LineOfBusinessCodeEnum.PERSONAL_LINES.equals(lineOfBusiness)) {
				
				if (policyHolderName != null && !policyHolderName.contains("null")) {
					firstName = policyHolderName.toUpperCase();
				}
				
			} else if (LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(lineOfBusiness) && !StringUtils.equalsIgnoreCase("PC", applicationMode)) {	
				
				if (driverInfoPiece != null) {
					
					if (this.validateInformationPieceChildPieces(driverInfoPiece.getChildren("firstDriver"), "firstDriver_firstName")) {
						firstName = driverInfoPiece.getChildren("firstDriver").getChildren("firstDriver_firstName").getPieces().get(1).getValue().toUpperCase();
					}	
					
					if (this.validateInformationPieceChildPieces(driverInfoPiece.getChildren("firstDriver"), "firstDriver_lastName")) {
						lastName = driverInfoPiece.getChildren("firstDriver").getChildren("firstDriver_lastName").getPieces().get(1).getValue().toUpperCase();
					}	
				}
			} else {
				
				if (generalInfoPiece != null) {
					
					if (this.validateInformationPieceChildPieces(generalInfoPiece.getChildren("general"), "general_principalOwnerName")) {
						// As we cannot know where the split is between the first name and the last name, full name is put in the first name
						firstName = generalInfoPiece.getChildren("general").getChildren("general_principalOwnerName").getPieces().get(1).getValue().toUpperCase();
					}	
				}
			}
			
			dialerParty.setFirstName(firstName);
			dialerParty.setLastName(lastName);
		}
	}
	
	
	/**
	 * Function to obtain the quote's province from its address field. 
	 * For now, should only be used for P&C to get a more specific province than the one converted from the selected underwriting company.
	 * @param generalInfoPiece The InformationPieceTO object containing the address_insured field
	 * @return province The province found in the address
	 */
	protected String obtainProvinceFromAddressInsuredField(InformationPieceTO generalInfoPiece) {
		String province = null;

		//Find the address_insured field in the general InformationPieceTO object
		if(generalInfoPiece != null && this.validateInformationPieceChildPieces(generalInfoPiece.getChildren("general") , "address_insured")) {
	
			String addressInsured = generalInfoPiece.getChildren("general").getChildren("address_insured").getPieces().get(1).getValue();
			// Obtain the province from the address field (should be 2nd info before last)
			String[] splitAddress = addressInsured.replaceAll(",", "").split(" ");
			
			if (splitAddress.length >= 3) {
				province = splitAddress[splitAddress.length - 3];
			}
		}
		
		return province;
	}

	/**
	 * Function to obtain the client phone number and create the corresponding PhoneDTO object if found. Returns null if no phone is found.
	 * @param infoPiece The general InformationPieceTO object containing either the general_homePhone or the general_cellPhone fields
	 * @return PhoneDTO the PhoneDTO object corresponding to the client's phone number
	 */
	protected PhoneDTO obtainClientPhoneNumber(InformationPieceTO infoPiece) {
		String basePhoneNumber = null;
		PhoneDTO dialerPhoneDTO = null;
		
		// The client should only have 1 phone number, either the general_homePhone or the general_cellPhone
		if (infoPiece != null && infoPiece.getChildren("general") != null ) {	
			
			if(this.validateInformationPieceChildPieces(infoPiece.getChildren("general"), "general_homePhone")) {
				basePhoneNumber = infoPiece.getChildren("general").getChildren("general_homePhone").getPieces().get(1).getValue().replaceAll("[^0-9+]", "");
			}	
			else if(this.validateInformationPieceChildPieces(infoPiece.getChildren("general"), "general_cellPhone")) {
				basePhoneNumber = infoPiece.getChildren("general").getChildren("general_cellPhone").getPieces().get(1).getValue().replaceAll("[^0-9+]", "");
			}	
		}
		
		// If a phone number was found, create the corresponding PhoneDTO
		if (StringUtils.isNotBlank(basePhoneNumber) && basePhoneNumber.length() > 3) {
			dialerPhoneDTO = new PhoneDTO();
			dialerPhoneDTO.setAreaCode(basePhoneNumber.substring(0, 3));
			dialerPhoneDTO.setPhoneNumber(basePhoneNumber.substring(3));
		}
		
		return dialerPhoneDTO;
	}
	
	/**
	 * Private function used to validate an InformationPieceTO's child piece with the following rules : 
	 * 	- Parent InforamtionPieceTO is not null
	 *  - Child piece is not null
	 *  - Child's pieces list is not null not empty and contains more than one piece (at least key and value)
	 *  - Child piece's sub-piece value is not null
	 * @param infoPiece The parent InformationPieceTO containing the child to validate
	 * @param childName The name of the child piece to validate
	 * @return
	 */
	protected boolean validateInformationPieceChildPieces(InformationPieceTO infoPiece, String childName) {
		return infoPiece != null
				&& infoPiece.getChildren(childName) != null
				&& infoPiece.getChildren(childName).getPieces() != null
				&& !infoPiece.getChildren(childName).getPieces().isEmpty()
				&& infoPiece.getChildren(childName).getPieces().size() > 1
				&& infoPiece.getChildren(childName).getPieces().get(1) != null
				&&  infoPiece.getChildren(childName).getPieces().get(1).getValue() != null;
	}
	
	/**
	 * Function used to convert a company String (A, 3 or 6) into the corresponding SourceUnderwritingCompanyEnum object
	 * @param selectedCompany The company to convert
	 * @return SourceUnderwritingCompanyEnum Corresponding SourceUnderwritingCompanyEnum object
	 */
	protected SourceUnderwritingCompanyEnum convertCompanyToSourceUnderwritingCompanyEnum(String selectedCompany) {
		SourceUnderwritingCompanyEnum dialerSourceUnderwritingCompany = null;
		
		if (StringUtils.equalsIgnoreCase("A", selectedCompany)) {
			dialerSourceUnderwritingCompany = SourceUnderwritingCompanyEnum.QUEBEC;
		} else if (StringUtils.equalsIgnoreCase("6", selectedCompany)) {
			dialerSourceUnderwritingCompany = SourceUnderwritingCompanyEnum.CENTRAL_ATLANTIC;
		} else if (StringUtils.equalsIgnoreCase("3", selectedCompany)) {
			dialerSourceUnderwritingCompany = SourceUnderwritingCompanyEnum.WESTERN;
		}
		
		return dialerSourceUnderwritingCompany;
	}

	/**
	 * Function used to convert an application mode (QF, QA, QH, etc.) into the corresponding QuoteAppEnumEnum object.
	 * @param applicationMode The application mode to convert.
	 * @return QuoteAppEnumEnum Corresponding QuoteAppEnumEnum object
	 */
	protected QuoteAppEnumEnum convertApplicationModeToQuoteAppEnum(String applicationMode) {
		QuoteAppEnumEnum dialerQuoteApp = null;

		if (StringUtils.equalsIgnoreCase("QF", applicationMode)) {
			dialerQuoteApp = QuoteAppEnumEnum.AUTO_QUICKQUOTE;
		} else if (StringUtils.equalsIgnoreCase("QA", applicationMode)) {
			dialerQuoteApp = QuoteAppEnumEnum.AUTOQUOTE;
		} else if (StringUtils.equalsIgnoreCase("IR", applicationMode)) {
			dialerQuoteApp = QuoteAppEnumEnum.IRCA_QUICKQUOTE;
		} else if (StringUtils.equalsIgnoreCase("QH", applicationMode) || StringUtils.equalsIgnoreCase("QC", applicationMode) || StringUtils.equalsIgnoreCase("QT", applicationMode)) {
			dialerQuoteApp = QuoteAppEnumEnum.HOME_QUICKQUOTE;
		} else if (StringUtils.equalsIgnoreCase("PC", applicationMode)) {
			dialerQuoteApp = QuoteAppEnumEnum.COMMERCIAL_QUICKQUOTE;
		} else if (StringUtils.equalsIgnoreCase("BA", applicationMode)) {
      dialerQuoteApp = QuoteAppEnumEnum.BUNDLE_QUOTE;
    } else if (StringUtils.equalsIgnoreCase("BH", applicationMode)) {
      dialerQuoteApp = QuoteAppEnumEnum.BUNDLE_QUOTE;
    } else if (StringUtils.equalsIgnoreCase("BC", applicationMode)) {
      dialerQuoteApp = QuoteAppEnumEnum.BUNDLE_QUOTE;
    } else if (StringUtils.equalsIgnoreCase("BT", applicationMode)) {
      dialerQuoteApp = QuoteAppEnumEnum.BUNDLE_QUOTE;
    }
		
		return dialerQuoteApp;
	}
	
	/**
	 * Function used to convert a LineOfInsuranceCodeEnum into the corresponding DatasourceOriginEnum object.
	 * @param quoteAppEnum The QuoteAppEnumEnum to convert
	 * @return DatasourceOriginEnum corresponding DatasourceOriginEnum object
	 */
  protected DatasourceOriginEnum convertQuoteAppEnumToDatasourceOriginEnum(QuoteAppEnumEnum quoteAppEnum) {
    String datasource = null;
    DatasourceOriginEnum datasourceOrigin = null;
    if (QuoteAppEnumEnum.BUNDLE_QUOTE.equals(quoteAppEnum)) {
      datasource = "XPAS";
    } else if (QuoteAppEnumEnum.AUTO_QUICKQUOTE.equals(quoteAppEnum)) {
      datasource = "PLP";
    } else if (QuoteAppEnumEnum.COMMERCIAL_QUICKQUOTE.equals(quoteAppEnum)) {
      datasource = "XPAS_CL";
    } else if (QuoteAppEnumEnum.AUTOQUOTE.equals(quoteAppEnum)) {
      datasource = "PLP";
    } else if (QuoteAppEnumEnum.HOME_QUICKQUOTE.equals(quoteAppEnum)) {
      datasource = "XPAS";
    } else if (QuoteAppEnumEnum.IRCA_QUICKQUOTE.equals(quoteAppEnum)) {
      datasource = "PLP";
    }

    if (datasource != null) {
      datasourceOrigin = DatasourceOriginEnum.fromValue(datasource);
    }

    return datasourceOrigin;
  }
}
