package com.intact.brokeroffice.service.search;

import java.util.ArrayList;
import java.util.List;

import com.intact.business.service.broker.domain.IQuoteDetail;

public class QuoteData {
	
	private List<IQuoteDetail> quotes = null;

	private List<String> exceptions = null;

	public QuoteData() {
		this.setExceptions(new ArrayList<String>());
		this.setQuotes(new ArrayList<IQuoteDetail>());
	}

	public List<IQuoteDetail> getQuotes() {
		return this.quotes;
	}

	public void setQuotes(List<IQuoteDetail> quotes) {
		this.quotes = quotes;
	}

	public List<String> getExceptions() {
		return this.exceptions;
	}

	public void setExceptions(List<String> exceptions) {
		this.exceptions = exceptions;
	}

}
