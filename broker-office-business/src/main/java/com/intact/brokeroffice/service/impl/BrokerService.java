package com.intact.brokeroffice.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import com.intact.tools.logging.exception.LoggingServiceException;
import com.intact.tools.logging.service.LoggingApplicationService;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.enums.UserActivityTypeCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.business.exception.BusinessServiceException;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.business.quote.QuotesDTO;
import com.intact.brokeroffice.business.quote.UploadProcessor;
import com.intact.brokeroffice.business.quote.util.PDFDocumentInfo;
import com.intact.brokeroffice.business.quote.util.PolicyInfo;
import com.intact.brokeroffice.business.quote.util.QuotesBeanBuilder;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.service.IBrokerService;
import com.intact.brokeroffice.service.search.DefaultBrokerService;
import com.intact.brokeroffice.service.search.MatchClient;
import com.intact.brokeroffice.service.search.MultiThreadBrokerService;
import com.intact.brokeroffice.service.search.QuoteData;
import com.intact.brokeroffice.service.search.QuoteDetailComparator;
import com.intact.brokeroffice.service.search.builder.QuoteSearchBuilder;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.business.service.broker.common.domain.QuoteNote;
import com.intact.business.service.broker.common.domain.QuoteReassignment;
import com.intact.business.service.broker.domain.IExtendedQuoteSearch;
import com.intact.business.service.broker.domain.IPDFDocument;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IQuoteDetail;
import com.intact.business.service.broker.domain.IQuoteNote;
import com.intact.business.service.broker.domain.IQuoteReassignment;
import com.intact.business.service.broker.domain.IQuoteSearch;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.broker.domain.QuoteStatus;
import com.intact.plt.domain.broker.FollowUpStatusEnum;
//import com.intact.plt.search.service.client.ClientSearchApplicationService;

@Service
public class BrokerService implements IBrokerService {

  static final List<String> CONTACT_APPLICATION_MODES = Arrays.asList("BA", "BH", "BC", "BT", "PC", "QH", "QT", "QC");
  /**
   * The policy version service.
   */
  @Autowired
  protected IPolicyVersionService policyVersionService = null;

  @Autowired
  @Qualifier("zzz")
  private MultiThreadBrokerService brokerService = null;

  @Inject
  @Named("subBrokersServiceOPC")
  private ISubBrokersService subBrokerService = null;

  @Inject
  private IInsurancePolicyService policyService = null;

  @Autowired
  @Qualifier("uploadProcessors")
  private Configuration uploadProcessors = null;

  @Autowired
  @Qualifier("max.row.company")
  private Configuration<String, Integer> maxRows = null;

  @Autowired
  @Qualifier("quote.builders")
  private Configuration<String, QuotesBeanBuilder> builders = null;

  @Inject
  private IPolicyVersionHelper policyVersionHelper = null;

  @Autowired
  private SystemDAO systemDAO = null;

//  @Autowired
//  private ClientSearchApplicationService searchService = null;

  @Autowired
  private QuotesBeanBuilder quotesBeanBuilder = null;

  private QuoteDetailComparator quoteComparator = null;

  @Autowired
  private LoggingApplicationService logService = null;

  public BrokerService() {
    this.setQuoteComparator(new QuoteDetailComparator());
  }

  public DefaultBrokerService getBrokerService() {
    return this.brokerService;
  }

  public void setBrokerService(DefaultBrokerService brokerService) {
    this.brokerService = (MultiThreadBrokerService) brokerService;
  }

  public QuoteDetailComparator getQuoteComparator() {
    return quoteComparator;
  }

  public void setQuoteComparator(QuoteDetailComparator quoteComparator) {
    this.quoteComparator = quoteComparator;
  }

  public Configuration getUploadProcessors() {
    return this.uploadProcessors;
  }

  public void setUploadProcessors(Configuration uploadProcessors) {
    this.uploadProcessors = uploadProcessors;
  }

  public ISubBrokersService getSubBrokerService() {
    return this.subBrokerService;
  }

  public void setSubBrokerService(ISubBrokersService subBrokerService) {
    this.subBrokerService = subBrokerService;
  }

  public IInsurancePolicyService getPolicyService() {
    return this.policyService;
  }

  public void setPolicyService(IInsurancePolicyService policyService) {
    this.policyService = policyService;
  }

  public Configuration<String, Integer> getMaxRows() {
    return this.maxRows;
  }

  public void setMaxRows(Configuration<String, Integer> maxRows) {
    this.maxRows = maxRows;
  }

  public Integer getMaxRows(String province) {
    return (Integer) this.getMaxRows().getValue(province);
  }

  public Configuration<String, QuotesBeanBuilder> getBuilders() {
    return builders;
  }

  public void setBuilders(Configuration<String, QuotesBeanBuilder> builders) {
    this.builders = builders;
  }

//  public ClientSearchApplicationService getSearchService() {
//    return this.searchService;
//  }
//
//  public void setSearchService(ClientSearchApplicationService searchService) {
//    this.searchService = searchService;
//  }

  public SystemDAO getSystemDAO() {
    return this.systemDAO;
  }

  public void setSystemDAO(SystemDAO systemDAO) {
    this.systemDAO = systemDAO;
  }

  public QuotesBeanBuilder getQuotesBeanBuilder() {
    return quotesBeanBuilder;
  }

  public void setQuotesBeanBuilder(QuotesBeanBuilder quotesBeanBuilder) {
    this.quotesBeanBuilder = quotesBeanBuilder;
  }


  @Override
  public QuotesDTO searchQuotes(IUserContext userContext, BrokerQuoteSearchCriteria criteria)
      throws BrokerServiceException {

    QuotesDTO quotes = null;
    QuoteData data = null;

    try {
      IQuoteSearch search = QuoteSearchBuilder.build(userContext, criteria);

      if (search instanceof IExtendedQuoteSearch quoteSearch) {
        data = this.getBrokerService().searchExtendedQuotes(userContext,
            quoteSearch);
      } else {
        data = this.getBrokerService().searchQuotes(userContext, search);
      }

      quotes = this.buildQuotes(userContext, data, true);
    } catch (Exception ex) {
      throw new BrokerServiceException(BrokerServiceException.EXEC_SEARCH_QUOTES_ERROR, ex, ex,
          this, userContext,
          criteria);
    }

    return quotes;
  }

  protected QuotesDTO buildQuotes(IUserContext userContext, QuoteData data, boolean showUploaded) {

    QuotesDTO quotes = new QuotesDTO();
    List<QuotesBean> list = new ArrayList<QuotesBean>();

    Collections.sort(data.getQuotes(), this.getQuoteComparator());

    int maxRow = (Integer) this.getMaxRows().getValue(userContext.getCompany());

    if (data.getQuotes().size() > maxRow) {
      data.setQuotes(data.getQuotes().subList(0, maxRow));
    }

    for (IQuoteDetail quote : data.getQuotes()) {
      // Remove uploaded quotes from results if necessary
      if (showUploaded ||
          (!QuoteStatus.UPLOADED.equals(quote.getStatus())
              && !QuoteStatus.UPLOADED_ACCEPTED.equals(quote.getStatus())
              && !QuoteStatus.UPLOADED_REFUSED.equals(quote.getStatus()))) {
        list.add(this.getBuilder(quote).build(quote, userContext, new QuotesBean()));
      }

    }

    quotes.setQuotes(MatchClient.match(list));
    quotes.setException(data.getExceptions());

    return quotes;
  }

  protected QuotesBeanBuilder getBuilder(IQuoteDetail quote) {
    return this.getBuilders().getValue(quote.getLineOfInsurance().getCode());
  }

  @Override
  public QuotesDTO listQuotes(IUserContext userContext) throws BrokerServiceException {

    QuotesDTO quotes = null;
    QuoteData data = null;

    try {
      data = this.getBrokerService().listQuotes(userContext);

      quotes = this.buildQuotes(userContext, data, false);
    } catch (Exception ex) {
      throw new BrokerServiceException(BrokerServiceException.EXEC_LIST_QUOTES_ERROR, ex, ex, this,
          userContext);
    }

    return quotes;
  }

  @Transactional
  @Override
  public void viewQuote(IUserContext context, Quote quote) throws BrokerServiceException {

    try {
      this.validate(context, quote);

      if (quote.getViewNote() == null) {
        throw new BrokerServiceException(BrokerServiceException.PARAM_QUOTE_INVALID);
      }

      com.intact.business.service.broker.common.domain.Quote newQuote = this
          .buildQuote(context, quote);

      this.getBrokerService().updateQuote(
          "webzone." + quote.getLineOfBusiness().getCode() + "." + quote.getLineOfInsurance()
              .getCode() + "." + context.getCompany(),
          newQuote, quote.getApplicationMode());
    } catch (BrokerServiceException bse) {
      throw bse;
    } catch (Exception ex) {
      throw new BrokerServiceException(BrokerServiceException.EXEC_VIEW_QUOTE_ERROR, ex, ex, this,
          context,
          quote);
    }
  }

  protected com.intact.business.service.broker.common.domain.Quote buildQuote(IUserContext context,
      Quote quote) {
    com.intact.business.service.broker.common.domain.Quote newQuote = new com.intact.business.service.broker.common.domain.Quote();

    newQuote.setId(quote.getId());
    newQuote.setStatus(quote.getStatus() != null ? quote.getStatus().getCode() : null);
    newQuote.setNotes(new ArrayList<IQuoteNote>());
    newQuote.setUser(context.getUser());

    if (quote.getLanguage() != null) {
      newQuote.setLanguage(quote.getLanguage());
    } else if (context.getLanguage() != null) {
      newQuote.setLanguage(context.getLanguage().getCode());
    }

    if (quote.getActivityNote() != null) {
      newQuote.getNotes()
          .add(new QuoteNote(UserActivityTypeCodeEnum.ADD_NOTE.getCode(), quote.getActivityNote()));
    }

    if (quote.getStatusNote() != null) {
      newQuote.getNotes().add(
          new QuoteNote(UserActivityTypeCodeEnum.CHANGE_FOLLOWUP_STATUS.getCode(),
              quote.getStatusNote()));
    }

    if (quote.getViewNote() != null) {
      newQuote.getNotes()
          .add(new QuoteNote(UserActivityTypeCodeEnum.VIEW_QUOTE.getCode(), quote.getViewNote()));
    }

    if (quote.getUploadNote() != null) {
      newQuote.getNotes()
          .add(new QuoteNote(UserActivityTypeCodeEnum.UPLOAD_QUOTE.getCode(), quote.getViewNote()));

      if (FollowUpStatusEnum.PDF_DOWNLOADED.getCode().equals(quote.getUploadNote())) {
        newQuote.setStatus(quote.getUploadNote());
      }

    }

    if (quote.getReassignNote() != null) {
      newQuote.getNotes()
          .add(new QuoteNote(UserActivityTypeCodeEnum.REASSIGNED_QUOTE.getCode(),
              quote.getViewNote()));
    }

    return newQuote;

  }

  @Override
  @Transactional
  public void updateQuote(IUserContext context, Quote quote) throws BrokerServiceException {
    try {
      this.getBrokerService().updateQuote(
          "webzone." + quote.getLineOfBusiness().getCode() + "." + quote.getLineOfInsurance()
              .getCode() + "." + context.getCompany(),
          this.buildQuote(context, quote), quote.getApplicationMode());
    } catch (com.intact.business.service.exception.BrokerServiceException ex) {
      throw new BrokerServiceException(ex.getMessage(), ex);
    }

  }

  @Override
  @Transactional
  public PolicyInfo uploadQuote(IUserContext context, Quote quote, String subBroker)
      throws BrokerServiceException {

    IPolicy policy = null;

    try {
      this.validate(context, quote, subBroker);
      // Reassign when quickquote
      if (this.getProcessor(quote.getLineOfInsurance()).isQuickQuote(context, quote)) {
        this.reassignQuote(context, quote);
      }

      // upload
      UploadProcessor processor = this.getProcessor(quote.getLineOfInsurance());
      policy = processor.execute(quote, subBroker);

      // add a note
      this.addUploadNote(context, quote);
    } catch (BrokerServiceException bse) {
      throw bse;
    } catch (Exception ex) {
      throw new BrokerServiceException(BrokerServiceException.EXEC_UPLOAD_QUOTE_ERROR, ex, ex, this,
          context,
          quote, subBroker);
    }

    return new PolicyInfo(policy.getId(), policy.getUri());
  }

  protected void validate(IUserContext context, Quote quote) throws BrokerServiceException {

    if (context == null) {
      throw new BrokerServiceException(BrokerServiceException.PARAM_CONTEXT_NULL);
    }

    if (quote == null) {
      throw new BrokerServiceException(BrokerServiceException.PARAM_QUOTE_NULL);
    }

    if (quote.getLineOfInsurance() == null || quote.getLineOfBusiness() == null) {
      throw new BrokerServiceException(BrokerServiceException.PARAM_QUOTE_INVALID, quote);
    }

    if (this.getBrokerService() == null) {
      throw new BrokerServiceException(BrokerServiceException.CONFIG_BROKER_SERVICE_NULL, this);
    }

  }

  protected void validate(IUserContext context, Quote quote, String subBroker)
      throws BrokerServiceException {

    this.validate(context, quote);

    if (subBroker == null) {
      throw new BrokerServiceException(BrokerServiceException.PARAM_SUB_BROKER_NULL);
    }

    if (this.getUploadProcessors() == null) {
      throw new BrokerServiceException(BrokerServiceException.CONFIG_UPLOAD_PROCESSORS_NULL, this);
    }

  }

  @Override
  public void addUploadNoteHome(IUserContext context, Quote quote) throws BrokerServiceException {
    try {
      this.addUploadNote(context, quote);
    } catch (com.intact.business.service.exception.BrokerServiceException e) {
      throw new BrokerServiceException(e.getMessage(), e);
    }
  }

  protected void addUploadNote(IUserContext context, Quote quote)
      throws com.intact.business.service.exception.BrokerServiceException {

    com.intact.business.service.broker.common.domain.Quote newQuote = this
        .buildQuote(context, quote);

    this.getBrokerService().updateQuote(
        "webzone." + quote.getLineOfBusiness().getCode() + "." + quote.getLineOfInsurance()
            .getCode() + "." + context.getCompany(),
        newQuote, quote.getApplicationMode());

  }

  @Override
  public void reassignQuote(IUserContext context, Quote quote) throws BrokerServiceException, LoggingServiceException {

    String master = this.getSystemDAO().getMasterBroker();

    this.logService.logInfo("info", "Broker with broker number = %s and company = %s  reassign quote with id = %s .".formatted(
				master, this.getSystemDAO().getCompany(), quote.getId()));

    if (master != null) {
      List<Quote> quotes = new ArrayList<Quote>();
      quotes.add(quote);
      ISubBrokers broker = this.getSubBrokerService()
          .getSubBroker(master, this.getSystemDAO().getCompany());

      if(broker == null) {
        this.logService.logInfo("info", "Broker with broker number = %s and company = %s  attempts to reassign quote with id = %s but the broker doesn't exist in database.".formatted(
						master, this.getSystemDAO().getCompany(), quote.getId()));

      }else {

        this.logService.logInfo("info", "Broker with broker number = %s, broker Id = %s and company =%s is reassigning quote with id = %s .".formatted(
						master, ("" + broker.getSubBrokerId()), this.getSystemDAO().getCompany(), quote.getId()));
      }
      this.reassignQuotes(context, quotes, "" + broker.getSubBrokerId());
    }

  }

  public List<String> uploadToSaversFromTestTool(Long insurancePolicyId, String anHalcionUserId)
      throws BusinessServiceException {
    List<String> results = new ArrayList<String>();

    InsurancePolicy insurancePolicy = this.getPolicyService().findById(insurancePolicyId);
    // this.upload(insurancePolicy);
    PolicyVersion policyVersion = this.policyVersionHelper
        .getLastPolicyVersionForInsurancePolicy(insurancePolicy);
    results.add(policyVersion.getId().toString());
    return results;
  }

  protected UploadProcessor getProcessor(LineOfInsuranceCodeEnum lineOfInsurance)
      throws BrokerServiceException {
    UploadProcessor processor = (UploadProcessor) this.getUploadProcessors()
        .getValue(lineOfInsurance.getCode());

    if (processor == null) {
      throw new BrokerServiceException(BrokerServiceException.CONFIG_UPLOAD_PROCESSOR_NOT_FOUND,
          this,
          lineOfInsurance.getCode());
    }

    return processor;
  }

  @Override
  @Transactional
  public void reassignQuotes(IUserContext context, List<Quote> quotes, String broker)
      throws BrokerServiceException {

    Map<String, List<Quote>> newQuotes = new HashMap<String, List<Quote>>();
    List<Quote> list = null;

    try {
      ISubBrokers subBroker = this.getSubBrokerService().getSubBrokerById(Long.parseLong(broker));

      for (Quote quote : quotes) {
        String key =
            quote.getLineOfBusiness().getCode() + "." + quote.getLineOfInsurance().getCode() + "."
                + context.getCompany();
        list = newQuotes.get(key);

        if (list == null) {
          list = new ArrayList<Quote>();
          newQuotes.put(key, list);
        }

        list.add(quote);
      }

      for (Entry<String, List<Quote>> entry : newQuotes.entrySet()) {
        this.getBrokerService().reassignQuotes("webzone." + entry.getKey(),
            this.builReassignment(context, entry.getValue(),
                isContactQuote(entry.getValue().get(0).getApplicationMode())
                    ? subBroker.getSubBrokerNumber()
                    : broker,
                subBroker.getNameLine1()
                    + (subBroker.getNameLine2() != null ? " " + subBroker.getNameLine2() : ""),
                context.getUser()), entry.getValue().get(0).getApplicationMode());
      }
    } catch (Exception ex) {
      throw new BrokerServiceException(ex.getMessage(), ex);
    }

  }

  private boolean isContactQuote(String applicationMode) {
    return (applicationMode!=null ? CONTACT_APPLICATION_MODES.contains(applicationMode) : false);
  }

  protected IQuoteReassignment builReassignment(IUserContext context, List<Quote> quotes,
      String broker,
      String brokerName, String user) {

    QuoteReassignment reassignment = new QuoteReassignment();

    reassignment.setBroker(broker);
    reassignment.setBrokerName(brokerName);
    reassignment.setUser(user);
    reassignment.setQuotes(new ArrayList<String>());

    for (Quote quote : quotes) {
      reassignment.getQuotes().add(quote.getId());
    }

    return reassignment;
  }

  public String toString() {
    return "Quote Business Process";
  }

  @Override
  public PDFDocumentInfo downloadDocument(IUserContext context, Quote quote)
      throws BrokerServiceException {

    IPDFDocument resultPDF = null;

    try {
      this.validate(context, quote);
      this.reassignQuote(context, quote);
      // add a note
      this.addUploadNote(context, quote);

      resultPDF = this.getBrokerService().downloadDocument(
          "webzone." + quote.getLineOfBusiness().getCode() + "." + quote.getLineOfInsurance()
              .getCode() + "." + context.getCompany(),
          this.buildQuote(context, quote), quote.getApplicationMode());

      return new PDFDocumentInfo(resultPDF.getUUID(), resultPDF.getDocument());

    } catch (BrokerServiceException bse) {
      throw bse;
    } catch (Exception ex) {
      throw new BrokerServiceException(BrokerServiceException.EXEC_UPLOAD_QUOTE_ERROR, ex, ex, this,
          context,
          quote);
    }
  }
}
