package com.intact.brokeroffice.service.domain.context;

public enum GroupType {

	BROKER_ADVISOR("BA", UserType.BROKER), BROKER_REASSIGN("BR", UserType.BROKER), BROKER_UNDERWRITER("BU", UserType.BROKER), ADMIN("AD", UserType.ADMIN), PROGRAM_ADMIN("PA", UserType.ADMIN), QUOTE_ADMIN("QA", UserType.ADMIN);
	
	private String code = null;
	private UserType type = null;
	
	private GroupType(String code, UserType type) {
		this.setCode(code);
		this.setType(type);
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
	public UserType getType() {
		return this.type;
	}

	public void setType(UserType type) {
		this.type = type;
	}

	public String toString() {
		return this.getCode();
	}

}
