# This is a HELM values file which feeds values into the common chart templates at https://githubifc.iad.ca.inet/lab-se/helm-commons-chart/tree/main/helm-commons-chart/templates
common:
  # development, staging, production
  environment: development
  container:
    env:
      - name: ENVIRONMENT
        value: intg

  additionalMounts:
    - name: secrets-volume
      mountPath: /mnt/secrets
  additionalVolumes:
    - name: secrets-volume
      secret:
        secretName: quoters-intg.secrets

  routes:
    - name: autoquote-intact-webservice-web-argocd
      targetPort: web
      enabled: true
      targetService: autoquoteintactwebserviceweb-argocd
      hostOverride: quoters-intg-autoquote-intact-webservice-web-argocd.apps.np.iad.ca.inet

  configmap_files:
    - name: configmap.properties
      data: |
        #### JNDI : server.xml ####
        jdbc.plp.driverClassName=oracle.jdbc.OracleDriver
        jdbc.plp.factory=oracle.jdbc.pool.OracleDataSourceFactory
        jdbc.plp.type=oracle.jdbc.xa.client.OracleXADataSource
        jdbc.plp.initialSize=1
        jdbc.plp.minIdle=5
        jdbc.plp.maxIdle=10
        jdbc.plp.maxActive=20
        jdbc.plp.removeAbandoned=true
        jdbc.plp.removeAbandonedTimeout=300
        jdbc.plp.validationQuery=select 1 from dual
        jdbc.plp.maxAge=3600000
        jdbc.plp.testOnBorrow=true
        jdbc.plp.testOnReturn=true
        jdbc.plp.testWhileIdle=true
        jdbc.plp.connectionCachingEnabled=true
        jdbc.plp.implicitCachingEnabled=true
        jdbc.plp.connectionCacheProperties=(ValidateConnection=true, InitialLimit=1, MinLimit=5, MaxLimit=20, ConnectionWaitTimeout=120, AbandonedConnectionTimeout=90, TimeToLiveTimeout=1800, InactivityTimeout=90000, PropertyCheckInterval=300)
        jdbc.oracle.url=**********************************************************************************************************************************)))
        
        jdbc.cif.driverClassName=oracle.jdbc.OracleDriver
        jdbc.cif.factory=oracle.jdbc.pool.OracleDataSourceFactory
        jdbc.cif.type=oracle.jdbc.xa.client.OracleXADataSource
        jdbc.cif.initialSize=1
        jdbc.cif.minIdle=5
        jdbc.cif.maxIdle=10
        jdbc.cif.maxActive=20
        jdbc.cif.removeAbandoned=true
        jdbc.cif.removeAbandonedTimeout=300
        jdbc.cif.validationQuery=select 1 from dual
        jdbc.cif.maxAge=3600000
        jdbc.cif.testOnBorrow=true
        jdbc.cif.testOnReturn=true
        jdbc.cif.testWhileIdle=true
        jdbc.cif.connectionCachingEnabled=true
        jdbc.cif.implicitCachingEnabled=true
        jdbc.cif.connectionCacheProperties=(ValidateConnection=true, InitialLimit=1, MinLimit=5, MaxLimit=20, ConnectionWaitTimeout=120, AbandonedConnectionTimeout=90, TimeToLiveTimeout=1800, InactivityTimeout=90000, PropertyCheckInterval=300)
        
        #### letter ####
        p.env=intg
        p.letter=-a
        
        
        ###access-manager-remote-service.properties
        HTTPS_AUTHENTICATION_PROTOCOL=SSL_CLIENT_AUTHENTICATION
        CLIENT_ACCOUNT_SERVICE_HOST=https://intg-accessmanager-intact.iad.ca.inet:55464
        USER_ACCOUNT_SERVICE_HOST=https://intg-accessmanager-intact.iad.ca.inet:55464
        
        KEYSTORE_URL=/usr/local/tomcat/access-manager-certificates/intact/intg/keystore-accessmanager.jks
        KEYSTORE_PASSWORD=${access.manager.keystore.intact.password}
        TRUSTSTORE_URL=/usr/local/tomcat/access-manager-certificates/intact/intg/truststore-accessmanager.jks
        TRUSTSTORE_PASSWORD=${access.manager.truststore.intact.password}
        
        ###autoquote-facade.properties
        jira.enableBugReport=false
        
        ### capi-services.properties
        capi.env=intg
        environnement-level=TE1
        webmethods-endpoint=https://intg-webmethods.iad.ca.inet:9410/ws/SNB.descriptors:
        home-page-url=https://intg-intact-sso.iad.ca.inet/wep/WEP/homePage.do
        helptext.documentRoot=https://intg-intact-sso.iad.ca.inet/on/secure/files/apps/helptext/
        workitem-webservice-username=
        workitem-webservice-password=
        application-image-url.env=intg
        application-image-url.belairdirect=-intact
        application-image-url.domain=-sso.iad.ca.inet
        application-image-url.junction=
        
        ###workitem.services.properties
        workitem.services.url=
        workitem.services.user=
        
        ### log4j2.xml
        log4j2.path=/opt/webserver/logs
        
        ###compatibility log4j 1.x
        log4j1.compatibility=true
        
        ###log4j2.xml
        log4j2.configuration.debug=false
        log4j2.ConsoleAppender.Threshold=INFO
        log4j2.root.level=INFO
        
        ### log4j.xml
        log4j.path=/opt/webserver/logs
        Threshold=INFO
        level_ROOT=INFO
        level_COMMON_INFO=ERROR
        level_CIF_INFO=INFO
        level_PLP_INFO=INFO
        level_SS_INFO=ERROR
        level_WEB_INFO=INFO
        level_INTACT_INFO=INFO
        level_AOP_INFO=INFO
        level_MONGO_INFO=INFO
        appender.rateManager=ConsoleAppender
        appender.dm2som=ConsoleAppender
        appender.dm2somAuto=ConsoleAppender
        appender.dm2somUsage=ConsoleAppender
        appender.dm2somOffer=ConsoleAppender
        appender.dm2pl=ConsoleAppender
        appender.ratingSimplification=ConsoleAppender
        appender.serviceCalls=ConsoleAppender
        appender.barcode=ConsoleAppender
        appender.ratingPerformance=ConsoleAppender
        appender.updateCoverages=ConsoleAppender
        appender.root=ConsoleAppender
        
        #log4j1 compatibility
        #log4j1.compatibility=true
        
        # autoquote-web.properties
        maintenance.page.roadblock=https://intg-intact-sso.iad.ca.inet/files/apps/maintenance/maintenance.htm
        production.environment=false
        technical.error.stacktrace.always=true
        brokerLocatorUrl.en=https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=EN
        brokerLocatorUrl.fr=https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=FR
        default.subBroker.number=0060
        document.root=https://intg-intact-sso.iad.ca.inet/on-w/secure/files/apps
        dynamic_banner_url=https://intg-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic_banner/
        dynamic_feedback_url=https://intg-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic_feedback/
        date.enable.BR16746.ON=2019-10-15
        
        ### security.properties
        security.auditlog.ip=************
        security.auditlog.port=22230
        
        ### ServiceLocatorRepository.xml
        serviceLocator.hostName=intg-pega.iad.ca.inet
        
        ### ILGeneralManagerPool.properties
        il.path=/opt/webserver/logs
        queueManagerName=MQA4
        hostname=MQA4
        port=1415
        channel=CLIENT.TO.MQA4
        userID=bloom
        password=${mqa4.bloom.password}
        
        ### cif-services.properties
        SHOW_SQL=false
        # /// create client with distributor ///
        CIF_CLIENT_CREATE_STORED_PROCEDURE_WITH_PARAMS=CIFADMIN.cif_class_person.add_object_person(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        CREATE_CLIENT_WITH_DIRECT_CHAN_DIST=true
        
        
        #autoquote-reloadable.properties
        config.advisor.activated=true
        config.web_tracking_google_account_id=UA-********-13
        config.web_tracking_omniture_account_id=ingcainsurancedev
        
        ### webattack.properties
        bypasswebattack.mail.smtp.host=smtp-gtw-np.iad.ca.inet
        bypasswebattackverification.mode=on
        
        ### Override rating date
        overridingRatingDate=********
        date.overridingRatingDate.AB=********
        date.overridingRatingDate.ON=
        
        # Configuration for barcodeService in /autoquote-backend-project/autoquote-facade/src/main/resources/autoquote-facade-beans.xml
        barcodeService.url=https://prep-barcodingservice.iad.ca.inet/wabr/barcodes
        
        # Configuration for clients claims history service
        gapiService.url=http://clientclaimshistoryserviceendpoint:8080
        
        ### notif-services.properties
        broker.env=intg
        public.images.uri=https://intg-intact-sso.iad.ca.inet/files/apps/email/images
        public.webzone.access.uri=https://intg-brokers.iad.ca.inet/webzone/WebZone/index.jsf?referenceNo=
        public.autoquote.images.uri=https://intg-intact-sso.iad.ca.inet/on/AutoQuote/image
        notif.env=.intg
        notif.incomplete.quote.override=<EMAIL>
        mail.smtp.host=uat-smtp.iad.ca.inet
        mail.smtp.username=${autoquote.intact.mail.smtp.username}
        mail.smtp.password=${autoquote.intact.mail.smtp.password}
        mail.smtp.auth=false
        mail.smtp.port=25
        mail.transport.protocol=smtp
        mail.smtp.starttls.enable=false
        mail.smtp.ssl.enable=false
        mail.debug=true
        minor_at_fault_date=2016-04-01
        
        base.url=https://intg-intact-sso.iad.ca.inet/wep/WEP/
        
        ### ESAPI validation.properties
        Logger.LogEncodingRequired=false
        
        config.ubi2.QC=true
        config.ubi2.ON=true
        config.ubi2.AB=true
        
        bloom-mq-handler-service.url=https://quoters-intg-bloom-mq-handler.apps.np.iad.ca.inet/mqhandler/v1/message
        bloom-mq-handler-service.isEnabled=true
        
        # logbook.properties
        logbook.filter.active=true
        mongo.logbook.hosts=stha2n14780.iad.ca.inet:27025
        mongo.logbook.replicaset=logbook01INTG
        mongo.logbook.databaseName=logbookdb
        mongo.logbook.authenticationDatabase=admin
        mongo.logbook.userName=logbook-appender-user
        mongo.logbook.password=${logbook.appender.mongo.password}
