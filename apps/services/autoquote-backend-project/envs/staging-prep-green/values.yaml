# This is a HELM values file which feeds values into the common chart templates at https://githubifc.iad.ca.inet/lab-se/helm-commons-chart/tree/main/helm-commons-chart/templates
common:
  # development, staging, production
  environment: staging
  appName: autoquote-intact-webservice-web-green
  container:
    env:
      - name: ENVIRONMENT
        value: prep

  additionalMounts:
    - name: secrets-volume
      mountPath: /mnt/secrets
  additionalVolumes:
    - name: secrets-volume
      secret:
        secretName: quoters-prep.secrets

  service:
    name: autoquoteintactwebservicewebgreen
  routes:
    - name: autoquote-intact-webservice-web-green
      targetPort: web
      enabled: true
      targetService: autoquoteintactwebservicewebgreen
      hostOverride: quoters-prep-autoquote-intact-webservice-web-green.apps.prep.iad.ca.inet

  configmap_files:
    - name: configmap.properties
      data: |
        #### JNDI : server.xml ####
        jdbc.plp.driverClassName=oracle.jdbc.OracleDriver
        jdbc.plp.factory=oracle.jdbc.pool.OracleDataSourceFactory
        jdbc.plp.type=oracle.jdbc.xa.client.OracleXADataSource
        jdbc.plp.initialSize=1
        jdbc.plp.minIdle=5
        jdbc.plp.maxIdle=10
        jdbc.plp.maxActive=20
        jdbc.plp.removeAbandoned=true
        jdbc.plp.removeAbandonedTimeout=300
        jdbc.plp.validationQuery=select 1 from dual
        jdbc.plp.maxAge=3600000
        jdbc.plp.testOnBorrow=true
        jdbc.plp.testOnReturn=true
        jdbc.plp.testWhileIdle=true
        jdbc.plp.connectionCachingEnabled=true
        jdbc.plp.implicitCachingEnabled=true
        jdbc.plp.connectionCacheProperties=(ValidateConnection=true, InitialLimit=1, MinLimit=5, MaxLimit=20, ConnectionWaitTimeout=120, AbandonedConnectionTimeout=90, TimeToLiveTimeout=1800, InactivityTimeout=90000, PropertyCheckInterval=300)
        jdbc.oracle.url=************************************************************************************************************************************ =2483))(CONNECT_DATA=(SERVER=DEDICATED)(service_name=dpsprep_HA.intact.net)))(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=odb-dpsprep-stby)(PORT =2483))(CONNECT_DATA=(SERVER=DEDICATED)(service_name=dpsprep_HA.intact.net))))
        
        jdbc.cif.driverClassName=oracle.jdbc.OracleDriver
        jdbc.cif.factory=oracle.jdbc.pool.OracleDataSourceFactory
        jdbc.cif.type=oracle.jdbc.xa.client.OracleXADataSource
        jdbc.cif.initialSize=1
        jdbc.cif.minIdle=5
        jdbc.cif.maxIdle=10
        jdbc.cif.maxActive=20
        jdbc.cif.removeAbandoned=true
        jdbc.cif.removeAbandonedTimeout=300
        jdbc.cif.validationQuery=select 1 from dual
        jdbc.cif.maxAge=3600000
        jdbc.cif.testOnBorrow=true
        jdbc.cif.testOnReturn=true
        jdbc.cif.testWhileIdle=true
        jdbc.cif.connectionCachingEnabled=true
        jdbc.cif.implicitCachingEnabled=true
        jdbc.cif.connectionCacheProperties=(ValidateConnection=true, InitialLimit=1, MinLimit=5, MaxLimit=20, ConnectionWaitTimeout=120, AbandonedConnectionTimeout=90, TimeToLiveTimeout=1800, InactivityTimeout=90000, PropertyCheckInterval=300)
        
        #### letter ####
        p.env=prep
        p.letter=
        
        ###access-manager-remote-service.properties
        HTTPS_AUTHENTICATION_PROTOCOL=SSL_CLIENT_AUTHENTICATION
        CLIENT_ACCOUNT_SERVICE_HOST=https://prep-accessmanager-intact-b2c.iad.ca.inet:55464
        USER_ACCOUNT_SERVICE_HOST=https://prep-accessmanager-intact-b2c.iad.ca.inet:55464
        
        KEYSTORE_URL=/usr/local/tomcat/access-manager-certificates/intact/prep/keystore-accessmanager.jks
        KEYSTORE_PASSWORD=${access.manager.keystore.intact.password}
        TRUSTSTORE_URL=/usr/local/tomcat/access-manager-certificates/intact/prep/truststore-accessmanager.jks
        TRUSTSTORE_PASSWORD=${access.manager.truststore.intact.password}
        
        ###autoquote-facade.properties
        jira.enableBugReport=false
        
        ### capi-services.properties
        capi.env=prep
        environnement-level=TE1
        webmethods-endpoint=https://prep-wmeth.iad.ca.inet:9410/ws/SNB.descriptors:
        home-page-url=https://prep-intact-sso.iad.ca.inet/wep/WEP/homePage.do
        helptext.documentRoot=https://prep-intact-sso.iad.ca.inet/on/secure/files/apps/helptext/
        workitem-webservice-username=
        workitem-webservice-password=
        application-image-url.env=prep
        application-image-url.belairdirect=-intact
        application-image-url.domain=-sso.iad.ca.inet
        application-image-url.junction=
        
        ###workitem.services.properties
        workitem.services.url=
        workitem.services.user=
        
        ###compatibility log4j 1.x
        log4j1.compatibility=true
        
        ###log4j2.xml
        log4j2.configuration.debug=false
        log4j2.ConsoleAppender.Threshold=INFO
        log4j2.root.level=INFO
        
        ### log4j.xml
        log4j.path=/opt/webserver/logs
        Threshold=DEBUG
        level_ROOT=DEBUG
        level_COMMON_INFO=ERROR
        level_CIF_INFO=ERROR
        level_PLP_INFO=DEBUG
        level_SS_INFO=DEBUG
        level_WEB_INFO=DEBUG
        level_INTACT_INFO=DEBUG
        level_AOP_INFO=DEBUG
        level_MONGO_INFO=ERROR
        appender.rateManager=ConsoleAppender
        appender.dm2som=ConsoleAppender
        appender.dm2somAuto=ConsoleAppender
        appender.dm2somUsage=ConsoleAppender
        appender.dm2somOffer=ConsoleAppender
        appender.dm2pl=ConsoleAppender
        appender.ratingSimplification=ConsoleAppender
        appender.serviceCalls=ConsoleAppender
        appender.barcode=ConsoleAppender
        appender.ratingPerformance=ConsoleAppender
        appender.updateCoverages=ConsoleAppender
        appender.root=ConsoleAppender
        
        
        # autoquote-web.properties
        maintenance.page.roadblock=https://prep-intact.iad.ca.inet/files/apps/maintenance/maintenance.htm
        production.environment=false
        technical.error.stacktrace.always=true
        brokerLocatorUrl.en = https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=EN
        brokerLocatorUrl.fr = https://intact.koremtest.com/?form=locator_search&CHANGEBROKER=Y&lang=FR
        default.subBroker.number=0060
        document.root=https://prep-intact-sso.iad.ca.inet/on/secure/files/apps
        dynamic_banner_url=https://prep-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic_banner/
        dynamic_feedback_url=https://prep-intact-sso.iad.ca.inet/on/secure/files/apps/dynamic_feedback/
        date.enable.BR16746.ON=2019-10-15
        
        ### security.properties
        security.auditlog.ip=************
        security.auditlog.port=22230
        
        ###  ServiceLocatorRepository.xml
        serviceLocator.hostName=prep-pega-blr.iad.ca.inet
        
        ### ILGeneralManagerPool.properties
        il.path=/opt/webserver/logs
        queueManagerName=MQR5C1
        hostname=MQR5C1
        port=1812
        channel=CLIENT.TO.MQR5C1
        userID=
        password=
        
        ### cif-services.properties
        SHOW_SQL=false
        # /// create client with distributor ///
        CIF_CLIENT_CREATE_STORED_PROCEDURE_WITH_PARAMS=CIFADMIN.cif_class_person.add_object_person(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        CREATE_CLIENT_WITH_DIRECT_CHAN_DIST=true
        
        
        #autoquote-reloadable.properties
        config.advisor.activated=true
        config.web_tracking_google_account_id=UA-********-13
        config.web_tracking_omniture_account_id=ingcainsurancedev
        
        ### webattack.properties
        bypasswebattack.mail.smtp.host=smtp-gtw-np.iad.ca.inet
        bypasswebattackverification.mode=off
        
        ### Override rating date
        overridingRatingDate=
        date.overridingRatingDate.AB=
        date.overridingRatingDate.ON=
        
        # Configuration for barcodeService in /autoquote-backend-project/autoquote-facade/src/main/resources/autoquote-facade-beans.xml
        barcodeService.url=https://prep-barcodingservice.iad.ca.inet/wabr/barcodes
        
        # Configuration for clients claims history service
        gapiService.url=http://clientclaimshistoryserviceendpoint:8080
        
        ### notif-services.properties
        broker.env=prep
        public.images.uri=https://prep-intact-sso.iad.ca.inet/files/apps/email/images
        public.webzone.access.uri=https://prep-brokers.iad.ca.inet/webzone/WebZone/index.jsf?referenceNo=
        public.autoquote.images.uri=https://prep-intact-sso.iad.ca.inet/on/AutoQuote/image
        notif.env=.prep
        notif.incomplete.quote.override=<EMAIL>
        mail.smtp.host=smtps.iad.ca.inet
        mail.smtp.username=${autoquote.intact.mail.smtp.username}
        mail.smtp.password=${autoquote.intact.mail.smtp.password}
        mail.smtp.auth=true
        mail.smtp.port=25
        mail.transport.protocol=smtp
        mail.smtp.starttls.enable=true
        mail.smtp.ssl.enable=false
        mail.debug=false
        minor_at_fault_date=2016-04-01
        
        
        base.url=https://prep-intact-sso.iad.ca.inet/wep/WEP/
        
        ### ESAPI validation.properties
        Logger.LogEncodingRequired=false
        
        config.ubi2.QC=true
        config.ubi2.ON=true
        config.ubi2.AB=true
        
        bloom-mq-handler-service.url=https://quoters-prep-bloom-mq-handler.apps.prep.iad.ca.inet/mqhandler/v1/message
        bloom-mq-handler-service.isEnabled=true
        
        # logbook properties
        mongo.logbook.hosts=stha2n17332.iad.ca.inet:27018,stha2n17335.iad.ca.inet:27018,stha2n17338.iad.ca.inet:27018
        mongo.logbook.replicaset=commonslab01PREP
        mongo.logbook.databaseName=logbookdb
        mongo.logbook.authenticationDatabase=admin
        mongo.logbook.userName=logbook-appender-user
        mongo.logbook.password=${logbook.appender.mongo.password}
