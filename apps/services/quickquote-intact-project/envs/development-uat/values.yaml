# This is a HELM values file which feeds values into the common chart templates at https://githubifc.iad.ca.inet/lab-se/helm-commons-chart/tree/main/helm-commons-chart/templates
common:
  # development, staging, production
  environment: development
  container:
    env:
      - name: ENVIRONMENT
        value: uat

  routes:
    - name: quickquote-intact-web
      enabled: true
      targetService: quickquote-intact-web-active
      hostOverride: quoters-uat-quickquote-intact-web.apps.np.iad.ca.inet

  additionalMounts:
    - name: secrets-volume
      mountPath: /mnt/secrets
  additionalVolumes:
    - name: secrets-volume
      secret:
        secretName: quoters-uat.secrets

  configmap_files:
    - name: configmap.properties
      data: |
        ###autoquote-webservice.properties
        autoquote.webservice.endpoint.url=http://autoquoteintactwebserviceweb:8080/AutoQuoteWS/services/AutoquoteFacadeWSEndpointPort
        
        ###commons-logging.properties
        
        ###log4j2.xml
        log4j2.configuration.debug=true
        log4j2.ConsoleAppender.Threshold=DEBUG
        log4j2.MongoAppender.Threshold=DEBUG
        log4j2.logger.level.intact=DEBUG
        log4j2.logger.level.ing=DEBUG
        log4j2.logger.level.springframework=INFO
        log4j2.logger.level.hibernate=INFO
        log4j2.root.level=DEBUG
        
        ###log4j.xml
        log4j.configuration.debug=true
        log4j.ConsoleAppender.Threshold=DEBUG
        log4j.MongoAppender.Threshold=INFO
        log4j.logger.level.intact=DEBUG
        log4j.logger.level.ing=DEBUG
        log4j.logger.level.springframework=INFO
        log4j.logger.level.hibernate=INFO
        log4j.root.level=DEBUG
        
        ###compatibility log4j 1.x
        log4j1.compatibility=true
        
        ###maintenance.properties
        maintenance.mode=auto
        production.environment=false
        maintenance.weekly.schedule=schedule.weekly.sunday=22:45/435/secret123
        maintenance.schedule=schedule.2014.10.08=08:30/1000/secret123
        
        ####quickquote-cache.properties
        
        ###security.properties
        security.auditlog.ip=************
        security.auditlog.port=23120
        security.auditlog.application=QuickQuoteIntact
        
        ### whiteliste province for IRCA
        whitelist.province=QC|AB|ON
        
        ####quickquote.properties
        javascript.logging=true
        
        #ubi mobile availability date (for texts only)
        ubi_mobile_available_date_ON=2016-01-01
        ubi_mobile_available_date_QC=2016-10-19
        ubi_mobile_available_date_AB=2017-01-01
        
        #### serviceAddress
        addressApiBaseUrl=https://apiuat.intactassurance.com/webquote-address
        
        #### serviceSendEmail
        sendEmailApiBaseUrl=https://apiuat.intactassurance.com/emailGateway/mail
        
        ### spoeMode
        spoeMode=true
        
        ### blue rush
        blue_ON=false
        blue_QC=false
        blue_AB=false
        blue_url=https://uat-intact-sso.iad.ca.inet/on/secure/files/apps/video/index.html
        
        
        # Urls to homeQuote
        homeQuote.url.qc=https://uat-intact-sso.iad.ca.inet/rqq/?
        homeQuote.url.on=https://uat-intact-sso.iad.ca.inet/rqq/?
        homeQuote.url.ab=https://uat-intact-sso.iad.ca.inet/rqq/?
        
        ### FILTERS
        logBookFilter.enabled=true
        
        # Mongo
        mongo.logbook.hosts=stha2n14783.iad.ca.inet:27021
        mongo.logbook.replicaset=logbook01UAT
        mongo.logbook.databaseName=logbookdb
        mongo.logbook.authenticationDatabase=admin
        mongo.logbook.userName=logbook-appender-user
        mongo.logbook.password=${logbook.appender.mongo.password}
        
        # Google Recaptcha
        google.recaptcha.enabled=true
        google.recaptcha.project.id=labse-318919
        #google.recaptcha.key.site
        recaptcha.keyid.invisible=6LeAWMwbAAAAABKWhTTGvBc5f4f--JGKHcal8ZpY

